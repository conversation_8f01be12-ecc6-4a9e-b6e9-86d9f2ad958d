import csv
import ftplib
import paramiko
import socket
import logging
import concurrent.futures
import ssl
from functools import partial

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_ftp(host, username, password, retry=1):
    for attempt in range(retry + 1):
        try:
            with ftplib.FTP(host, timeout=5) as ftp:
                ftp.set_pasv(True)
                ftp.login(username, password)
            logging.info(f"FTP Success: {username}@{host}")
            return True
        except Exception as e:
            logging.warning(f"FTP Failed (Attempt {attempt + 1}): {username}@{host} - {str(e)}")
    return False

def check_ssh(host, username, password, retry=1):
    for attempt in range(retry + 1):
        try:
            with paramiko.SSHClient() as ssh:
                ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                ssh.connect(host, username=username, password=password, timeout=5, 
                            allow_agent=False, look_for_keys=False, 
                            banner_timeout=5, auth_timeout=5)
            logging.info(f"SSH Success: {username}@{host}")
            return True
        except Exception as e:
            logging.warning(f"SSH Failed (Attempt {attempt + 1}): {username}@{host} - {str(e)}")
    return False

def check_credentials(host, row):
    user, password = row[0], row[1]
    ftp_result = check_ftp(host, user, password)
    ssh_result = check_ssh(host, user, password)
    return [user, password, str(ftp_result), str(ssh_result)]

def main():
    host = "vm1.webtogether.ie"
    input_file = "input.csv"
    output_file = "results.csv"

    # Disable SSL warnings
    ssl._create_default_https_context = ssl._create_unverified_context

    with open(input_file, 'r') as infile, open(output_file, 'w', newline='') as outfile:
        reader = csv.reader(infile, delimiter='|')
        writer = csv.writer(outfile)

        # Write header
        writer.writerow(['user', 'password', 'ftp working', 'ssh working'])

        # Create a partial function with the host argument filled in
        check_func = partial(check_credentials, host)

        # Use ThreadPoolExecutor for concurrent execution
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            results = list(executor.map(check_func, reader))

        # Write results
        writer.writerows(results)

    logging.info(f"Results have been written to {output_file}")

if __name__ == "__main__":
    main()