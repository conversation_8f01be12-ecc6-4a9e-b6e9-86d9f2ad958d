#!/bin/bash

# Set variables
FTP_HOST="************"
FTP_PORT="21"
S3_BUCKET="backup-big-bitbucket-repos"
CREDENTIALS_FILE="credentials.csv"
OUTPUT_FILE="output.csv"
LOG_FILE="/tmp/archiver.log"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to format time
format_time() {
    printf "%.2f" "$1"
}

# Check if required tools are installed
for cmd in lftp aws; do
    if ! command -v $cmd &> /dev/null; then
        log_message "Error: $cmd is required but not installed. Aborting."
        exit 1
    fi
done

# Check if credentials file exists
if [ ! -f "$CREDENTIALS_FILE" ]; then
    log_message "Error: Credentials file $CREDENTIALS_FILE not found. Aborting."
    exit 1
fi

# Create output CSV header
echo "user,size,ftp_time,s3_time,total_time,status" > "$OUTPUT_FILE"

# Process each credential
while IFS='|' read -r username password; do
    log_message "Processing $username"
    start_time=$(date +%s.%N)

    # Create temporary directory
    tmp_dir="/tmp/$username"
    mkdir -p "$tmp_dir"

    # Download httpdocs from FTP using lftp with optimized settings
    ftp_start=$(date +%s.%N)
    log_message "Starting FTP download of httpdocs for $username"
    if lftp -u "$username,$password" "ftp://$FTP_HOST:$FTP_PORT" \
        -e "set net:timeout 120; \
            set net:max-retries 5; \
            set net:reconnect-interval-base 5; \
            set net:reconnect-interval-multiplier 1; \
            set net:socket-buffer 1048576; \
            set ftp:ssl-allow no; \
            set ftp:passive-mode off; \
            set ftp:prefer-epsv false; \
            set mirror:parallel-transfer-count 20; \
            set mirror:use-pget-n 1; \
            set pget:min-chunk-size 1M; \
            mirror --parallel=20 --no-perms --no-umask --verbose --depth-first --no-empty-dir /httpdocs $tmp_dir/httpdocs; \
            quit" 2>&1 | tee -a "$LOG_FILE"; then
        ftp_status="Success"
    else
        ftp_status="Failed"
        log_message "FTP download failed for $username"
    fi
    ftp_end=$(date +%s.%N)
    ftp_time=$(echo "$ftp_end - $ftp_start" | bc)
    log_message "FTP download finished for $username in $(format_time $ftp_time)s"

    # Calculate size using du
    size=$(du -sh "$tmp_dir" | cut -f1)
    log_message "Size of $username: $size"

    # Upload to S3
    s3_start=$(date +%s.%N)
    if aws s3 sync "$tmp_dir" "s3://$S3_BUCKET/$username"; then
        s3_status="Success"
    else
        s3_status="Failed"
        log_message "S3 upload failed for $username"
    fi
    s3_end=$(date +%s.%N)
    s3_time=$(echo "$s3_end - $s3_start" | bc)
    log_message "S3 upload finished for $username in $(format_time $s3_time) s"

    # Clean up
    rm -rf "$tmp_dir"

    end_time=$(date +%s.%N)
    total_time=$(echo "$end_time - $start_time" | bc)

    # Determine overall status
    if [ "$ftp_status" = "Success" ] && [ "$s3_status" = "Success" ]; then
        overall_status="Success"
    else
        overall_status="Failed"
    fi

    # Log and output results
    log_message "Completed $username: $size, FTP: ${ftp_time}s, S3: ${s3_time}s, Status: $overall_status"
    echo "$username,$size,$(format_time $ftp_time),$(format_time $s3_time),$(format_time $total_time),$overall_status" >> "$OUTPUT_FILE"
done < "$CREDENTIALS_FILE"

log_message "All transfers completed"