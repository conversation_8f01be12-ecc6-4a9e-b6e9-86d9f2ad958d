#!/bin/bash

# Configuration
SOURCE_HOST="<EMAIL>"
SOURCE_DIR="/var/www/vhosts/"
DEST_DIR="./vhosts_backups"
LOG_DIR="./vhosts_backups/logs"
DATE=$(date +%Y%m%d)
TIME=$(date +%H%M%S)
LOG_FILE="$LOG_DIR/sync_$DATE.log"
PASS_FILE=".rsync_password.txt"

# Function to log messages
log_message() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$message" | tee -a "$LOG_FILE"
}

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Check if password file exists
if [ ! -f "$PASS_FILE" ]; then
    echo "Error: Password file not found at $PASS_FILE"
    exit 1
fi

# Read password
RSYNC_PASS=$(head -n1 "$PASS_FILE")
if [ -z "$RSYNC_PASS" ]; then
    log_message "Error: Password file is empty"
    exit 1
fi

# Test SSH connection
log_message "Testing SSH connection..."
if ! SSHPASS="$RSYNC_PASS" sshpass -e ssh "$SOURCE_HOST" echo "Connection successful" > /dev/null 2>&1; then
    log_message "Error: Could not connect to $SOURCE_HOST"
    exit 1
fi
log_message "SSH connection test successful"

# Start sync process
log_message "=== Starting rsync sync process ==="
log_message "Source: $SOURCE_HOST:$SOURCE_DIR"
log_message "Destination: $DEST_DIR"

SYNC_START_TIME=$(date +%s)

# Create temporary exclude patterns file
EXCLUDE_FILE=$(mktemp)
cat > "$EXCLUDE_FILE" << 'EOF'
# Folders to exclude
**/acme-challenge/
**/cache/
**/tmp/
**/temp/
**/sessions/
**/.git/
**/node_modules/

# Files to exclude
*.log
*.tmp
*.bak
*.swp
*.sess
*.session
*.lock
*.pid
*.cache
sess_*
EOF

# Perform rsync
SSHPASS="$RSYNC_PASS" sshpass -e rsync -avvz \
    --append \
    --ignore-existing \
    --progress \
    --rsync-path="sudo /usr/bin/rsync" \
    --exclude-from="$EXCLUDE_FILE" \
    "$SOURCE_HOST:$SOURCE_DIR" \
    "$DEST_DIR" >> "$LOG_FILE" 2>&1

SYNC_RESULT=$?

SYNC_END_TIME=$(date +%s)
SYNC_DURATION=$((SYNC_END_TIME - SYNC_START_TIME))

# Log results
if [ $SYNC_RESULT -eq 0 ]; then
    log_message "✓ Sync completed successfully"
    log_message "  - Duration: ${SYNC_DURATION} seconds"
    
    # Calculate total size synchronized
    TOTAL_SIZE=$(du -sh "$DEST_DIR" | cut -f1)
    log_message "  - Total destination size: ${TOTAL_SIZE}"
else
    log_message "✗ ERROR: Sync failed with exit code $SYNC_RESULT"
fi

# Clean up old logs
log_message "Cleaning up logs older than 30 days..."
find "$LOG_DIR" -type f -name "*.log" -mtime +30 -exec rm {} \;

# Log summary
log_message "=== Sync process completed ==="
log_message "Summary:"
log_message "- Duration: $SYNC_DURATION seconds"
log_message "- Status: $([ $SYNC_RESULT -eq 0 ] && echo "Success" || echo "Failed")"
log_message "- Log file: $LOG_FILE"