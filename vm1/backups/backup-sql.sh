#!/bin/bash

# Set variables
BACKUP_DIR="./mysql_backups"
LOG_DIR="./mysql_backups/logs"
DATE=$(date +%Y%m%d)
TIME=$(date +%H%M%S)
MYSQL_USER="backup"
MYSQL_HOST="vm1.webtogether.ie"
MYSQL_PASS_FILE=".mysql_password.txt"
LOG_FILE="$LOG_DIR/backup_$DATE.log"

# Function to log messages
log_message() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$message" | tee -a "$LOG_FILE"
}

# Check if password file exists
if [ ! -f "$MYSQL_PASS_FILE" ]; then
    echo "Error: Password file not found at $MYSQL_PASS_FILE"
    exit 1
fi

# Read and validate password
MYSQL_PASS=$(head -n1 "$MYSQL_PASS_FILE")
if [ -z "$MYSQL_PASS" ]; then
    echo "Error: Password file is empty"
    exit 1
fi

# Test MySQL connection before proceeding
if ! mysql -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASS" --protocol=TCP -e "SELECT 1" >/dev/null 2>&1; then
    echo "Error: Could not connect to MySQL server. Please check credentials."
    echo "Host: $MYSQL_HOST | User: $MYSQL_USER | Password: $MYSQL_PASS"
    exit 1
fi
log_message "Connected to MySQL server successfully"




# Create backup and log directories if they don't exist
mkdir -p "$BACKUP_DIR/$DATE"
mkdir -p "$LOG_DIR"

log_message "=== Starting MySQL backup process ==="
log_message "Backup directory: $BACKUP_DIR/$DATE"

# List of databases to backup
DATABASES=(
    "abbey_live_2346bn"
    "brantstone_live_324kj6b"
    "fssol_yrw6hg"
    "omgplswork78"
    "lockhartandcleary"
    "okc-staging"
    "pension_lv_3j5b"
    "raglanstreet_cdi"
    "timoney_816276"
    "jfl_435gfbh"
    "mcgovern_live_3kjb5_fde4"
    "gibney_liver3vhsa"
    "rbk_live_iubh23"
    "geaney_637fsj1"
    "icsmortgages_rt56"
)

# Initialize counters
SUCCESSFUL_BACKUPS=0
FAILED_BACKUPS=0

# Backup each database
for DB in "${DATABASES[@]}"
do
    log_message "Starting backup of $DB..."
    BACKUP_START_TIME=$(date +%s)
    
    mysqldump -h "$MYSQL_HOST" -u "$MYSQL_USER" -p"$MYSQL_PASS" --single-transaction --default-character-set=latin1 --quick --skip-extended-insert=FALSE --skip-add-locks --skip-comments --skip-triggers "$DB" | gzip > "$BACKUP_DIR/$DATE/$DB-$DATE.sql.gz"
    
    if [ $? -eq 0 ]; then
        BACKUP_END_TIME=$(date +%s)
        BACKUP_DURATION=$((BACKUP_END_TIME - BACKUP_START_TIME))
        FILE_SIZE=$(du -h "$BACKUP_DIR/$DATE/$DB-$DATE.sql.gz" | cut -f1)
        
        log_message "✓ Backup of $DB completed successfully"
        log_message "  - Duration: ${BACKUP_DURATION} seconds"
        log_message "  - Backup size: ${FILE_SIZE}"
        ((SUCCESSFUL_BACKUPS++))
    else
        log_message "✗ ERROR: Backup of $DB failed"
        ((FAILED_BACKUPS++))
    fi
done

# Clean up old backups
log_message "Cleaning up backups older than 30 days..."
find "$BACKUP_DIR" -type d -mtime +30 -exec rm -rf {} \;

# Log summary
log_message "=== Backup process completed ==="
log_message "Summary:"
log_message "- Total databases processed: ${#DATABASES[@]}"
log_message "- Successful backups: $SUCCESSFUL_BACKUPS"
log_message "- Failed backups: $FAILED_BACKUPS"
log_message "- Log file location: $LOG_FILE"