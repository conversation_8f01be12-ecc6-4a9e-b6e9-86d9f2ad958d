import requests
import logging
from dotenv import load_dotenv
import os
import time
import json
import hashlib

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_bitbucket_repo_info(workspace, repo_id, auth):
    url = f"https://api.bitbucket.org/2.0/repositories/{workspace}/{repo_id}"
    response = requests.get(url, auth=auth)
    if response.status_code == 200:
        return response.json()
    else:
        logging.error(f"Failed to get Bitbucket repo info for {repo_id}. Status code: {response.status_code}")
        logging.error(f"Response content: {response.text}")
        raise Exception(f"Failed to get Bitbucket repo info for {repo_id}")

def assign_repo_to_project(workspace, repo_slug, project_key, auth):
    url = f"https://api.bitbucket.org/2.0/repositories/{workspace}/{repo_slug}"
    data = {
        "project": {
            "key": project_key
        }
    }
    response = requests.put(url, auth=auth, json=data)
    if response.status_code == 200:
        return True
    else:
        logging.error(f"Failed to assign repository {repo_slug} to project {project_key}. Status code: {response.status_code}")
        logging.error(f"Response content: {response.text}")
        return False

def get_input_hash():
    with open('input.txt', 'rb') as file:
        return hashlib.md5(file.read()).hexdigest()

def get_repo_info(input_hash):
    filename = f"repo_info_{input_hash}.json"
    
    if os.path.exists(filename):
        logging.info(f"Using existing repo info from {filename}")
        with open(filename, 'r') as json_file:
            return json.load(json_file)
    
    # Read environment variables
    bitbucket_workspace = os.getenv('BITBUCKET_WORKSPACE')
    bitbucket_username = os.getenv('BITBUCKET_USERNAME')
    bitbucket_app_password = os.getenv('BITBUCKET_APP_PASSWORD')

    # Check if all required environment variables are set
    required_vars = ['BITBUCKET_WORKSPACE', 'BITBUCKET_USERNAME', 'BITBUCKET_APP_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        raise Exception("Missing required environment variables")

    bitbucket_auth = (bitbucket_username, bitbucket_app_password)

    with open('input.txt', 'r') as file:
        repo_ids = file.read().splitlines()

    repo_info_array = []

    for repo_id in repo_ids:
        try:
            data = get_bitbucket_repo_info(bitbucket_workspace, repo_id, bitbucket_auth)
            repo_info_array.append(data)
            time.sleep(1)  # Sleep for 1 second between requests
        except Exception as e:
            logging.error(f"Error processing repo {repo_id}: {str(e)}")
            raise  # Re-raise the exception to stop program execution

    # Save repo info to JSON file with input hash
    with open(filename, 'w') as json_file:
        json.dump(repo_info_array, json_file, indent=2)

    logging.info(f"Repo information saved to {filename}")
    return repo_info_array

def assign_repos_to_project(repo_info_array, project_key):
    bitbucket_workspace = os.getenv('BITBUCKET_WORKSPACE')
    bitbucket_username = os.getenv('BITBUCKET_USERNAME')
    bitbucket_app_password = os.getenv('BITBUCKET_APP_PASSWORD')

    # Check if all required environment variables are set
    required_vars = ['BITBUCKET_WORKSPACE', 'BITBUCKET_USERNAME', 'BITBUCKET_APP_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        raise Exception("Missing required environment variables")

    bitbucket_auth = (bitbucket_username, bitbucket_app_password)

    for repo_data in repo_info_array:
        repo_slug = repo_data['slug']
        try:
            if assign_repo_to_project(bitbucket_workspace, repo_slug, project_key, bitbucket_auth):
                logging.info(f"Successfully assigned {repo_slug} to project {project_key}")
            else:
                logging.warning(f"Failed to assign {repo_slug} to project {project_key}")
            time.sleep(1)  # Sleep for 1 second between requests
        except Exception as e:
            logging.error(f"Error assigning repo {repo_slug} to project: {str(e)}")
            # Continue with the next repository instead of stopping execution
            continue

def main():
    try:
        input_hash = get_input_hash()
        repo_info_array = get_repo_info(input_hash)
        project_key = os.getenv('BITBUCKET_PROJECT_KEY')
        if not project_key:
            raise Exception("BITBUCKET_PROJECT_KEY environment variable is not set")
        assign_repos_to_project(repo_info_array, project_key)
    except Exception as e:
        logging.error(f"Program execution stopped due to an error: {str(e)}")
        exit(1)

if __name__ == "__main__":
    main()