import requests
import logging
import time
from getpass import getpass

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_user_credentials():
    print("\nPlease enter your GitHub credentials:")
    github_org = input("GitHub Organization name: ").strip()
    github_token = getpass("GitHub Personal Access Token: ").strip()
    
    if not github_org or not github_token:
        raise ValueError("Organization name and GitHub token are required")
    
    return github_org, github_token

def create_github_repo(org, repo_name, headers):
    url = f"https://api.github.com/orgs/{org}/repos"
    data = {
        "name": repo_name,
        "private": True,
        "has_issues": False,
        "has_projects": False,
        "has_wiki": False,
    }
    response = requests.post(url, headers=headers, json=data)
    if response.status_code == 201:
        logging.info(f"Successfully created GitHub repository: {repo_name}")
        return True
    elif response.status_code == 422:
        logging.warning(f"GitHub repository already exists: {repo_name}")
        return False
    else:
        logging.error(f"Failed to create GitHub repository: {repo_name}. Status code: {response.status_code}")
        logging.error(f"Response: {response.text}")
        return False

def create_github_repos(repo_slugs, github_org, github_token):
    github_headers = {
        'Authorization': f'token {github_token}',
        'Accept': 'application/vnd.github.v3+json'
    }

    recreated_repos = []

    for repo_slug in repo_slugs:
        try:
            if create_github_repo(github_org, repo_slug, github_headers):
                recreated_repos.append(repo_slug)
            time.sleep(1)  # Sleep for 1 second between requests to avoid rate limiting
        except Exception as e:
            logging.error(f"Error creating GitHub repo {repo_slug}: {str(e)}")
            # Continue with the next repo instead of stopping the entire process
            continue

    return recreated_repos

def get_input_file():
    while True:
        file_path = input("\nEnter the path to the input file containing repository names: ").strip()
        try:
            with open(file_path, 'r') as input_file:
                return input_file.read().splitlines()
        except FileNotFoundError:
            print(f"Error: File '{file_path}' not found. Please try again.")
        except Exception as e:
            print(f"Error reading file: {str(e)}. Please try again.")

def main():
    try:
        # Get user credentials
        github_org, github_token = get_user_credentials()
        
        # Get repository names from input file
        repo_slugs = get_input_file()

        # Create GitHub repos
        recreated_repos = create_github_repos(repo_slugs, github_org, github_token)

        # Write output file
        output_file_path = 'recreated_repos.txt'
        with open(output_file_path, 'w') as output_file:
            for repo in recreated_repos:
                output_file.write(f"{repo}\n")

        logging.info(f"Successfully recreated {len(recreated_repos)} repositories. Results written to {output_file_path}")

    except Exception as e:
        logging.error(f"Program execution stopped due to an error: {str(e)}")
        exit(1)

if __name__ == "__main__":
    main()