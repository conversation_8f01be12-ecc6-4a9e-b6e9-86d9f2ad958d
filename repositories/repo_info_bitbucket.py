import os
import requests
import csv
from datetime import datetime
import time
import logging
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_bitbucket_api(url, auth, params=None):
    response = requests.get(url, auth=auth, params=params)
    response.raise_for_status()
    return response.json()

def get_all_items(url, auth, item_type):
    items = []
    page = 1
    while url:
        logging.info(f"Fetching page {page} of {item_type}...")
        data = get_bitbucket_api(url, auth)
        items.extend(data['values'])
        url = data.get('next')
        page += 1
    logging.info(f"Found {len(items)} {item_type}")
    return items

def get_repo_info(workspace, repo_slug, auth):
    logging.info(f"Processing repository: {repo_slug}")
    base_url = f"https://api.bitbucket.org/2.0/repositories/{workspace}/{repo_slug}"

    repo_info = get_bitbucket_api(base_url, auth)

    branches = get_all_items(f"{base_url}/refs/branches", auth, "branches")
    production_branch = next((b for b in branches if b['name'] in ['production', 'master']), None)

    if production_branch:
        last_commit = production_branch['target']
        last_commit_date = datetime.fromisoformat(last_commit['date'].rstrip('Z'))
        last_commit_author = last_commit['author']['raw']
        production_branch_name = production_branch['name']
    else:
        production_branch_name = last_commit_date = last_commit_author = 'N/A'

    return {
        'repo_id': repo_info['uuid'],
        'project': repo_info.get('project', {}).get('name', 'N/A'),
        'name': repo_info['name'],
        'repo_url': repo_info['links']['html']['href'],
        'created_on': datetime.fromisoformat(repo_info['created_on'].rstrip('Z')),
        'production_branch': production_branch_name,
        'branches': ','.join(b['name'] for b in branches),
        'last_commit_date': last_commit_date,
        'last_commit_author': last_commit_author,
    }

def generate_report(workspace, auth, output_file):
    logging.info(f"Starting report generation for workspace: {workspace}")

    repos_url = f"https://api.bitbucket.org/2.0/repositories/{workspace}"
    repos = get_all_items(repos_url, auth, "repositories")

    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['Repo ID', 'Project', 'Repository', 'Repo URL', 'Created On', 'Production Branch',
                      'Branches', 'Last Commit Date', 'Last Commit Author']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for i, repo in enumerate(repos, 1):
            logging.info(f"Processing repository {i}/{len(repos)}: {repo['name']}")
            repo_info = get_repo_info(workspace, repo['slug'], auth)

            writer.writerow({
                'Repo ID': repo_info['repo_id'],
                'Project': repo_info['project'],
                'Repository': repo_info['name'],
                'Repo URL': repo_info['repo_url'],
                'Created On': repo_info['created_on'],
                'Production Branch': repo_info['production_branch'],
                'Branches': repo_info['branches'],
                'Last Commit Date': repo_info['last_commit_date'],
                'Last Commit Author': repo_info['last_commit_author'],
            })

            # Add a small delay to avoid rate limiting
            time.sleep(0.5)

    logging.info(f"Report generation completed. Output saved to {output_file}")

if __name__ == "__main__":
    bitbucket_workspace = os.getenv('BITBUCKET_WORKSPACE')
    bitbucket_username = os.getenv('BITBUCKET_USERNAME')
    bitbucket_app_password = os.getenv('BITBUCKET_APP_PASSWORD')

    # Check if all required environment variables are set
    required_vars = ['BITBUCKET_WORKSPACE', 'BITBUCKET_USERNAME', 'BITBUCKET_APP_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        raise Exception("Missing required environment variables")

    output_file = f"bitbucket_report_{int(datetime.now().timestamp())}.csv"

    auth = (bitbucket_username, bitbucket_app_password)
    generate_report(bitbucket_workspace, auth, output_file)