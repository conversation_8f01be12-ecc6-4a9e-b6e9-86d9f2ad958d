import requests
import logging
from dotenv import load_dotenv
import os
import time
import json
from datetime import datetime

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_github_repos(org, headers):
    repos = []
    page = 1
    while True:
        url = f"https://api.github.com/orgs/{org}/repos?type=all&per_page=100&page={page}"
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            page_repos = response.json()
            if not page_repos:
                break
            repos.extend(page_repos)
            page += 1
            time.sleep(1)  # Sleep for 1 second between requests
        else:
            logging.error(f"Failed to get GitHub repos for {org}. Status code: {response.status_code}")
            raise Exception(f"Failed to get GitHub repos for {org}")
    return repos

def is_repo_empty(repo):
    return repo['size'] == 0

def detect_empty_repos():
    github_org = os.getenv('GITHUB_ORG')
    github_token = os.getenv('GITHUB_TOKEN')

    # Check if all required environment variables are set
    required_vars = ['GITHUB_ORG', 'GITHUB_TOKEN']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        raise Exception("Missing required environment variables")

    github_headers = {
        'Authorization': f'token {github_token}',
        'Accept': 'application/vnd.github.v3+json'
    }

    try:
        repos = get_github_repos(github_org, github_headers)
        logging.info(f"Retrieved {len(repos)} repositories from GitHub")
    except Exception as e:
        logging.error(f"Error retrieving repositories: {str(e)}")
        raise

    empty_repos = []
    non_empty_repos = []

    for repo in repos:
        repo_name = repo['name']
        if is_repo_empty(repo):
            empty_repos.append(repo_name)
            logging.info(f"Repository {repo_name} is empty.")
        else:
            non_empty_repos.append(repo_name)
            logging.info(f"Repository {repo_name} is not empty.")

    # Save results to files
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    empty_repos_file = f'empty_repos_{timestamp}.txt'
    non_empty_repos_file = f'non_empty_repos_{timestamp}.txt'
    
    with open(empty_repos_file, 'w') as file:
        file.write('\n'.join(empty_repos))
    with open(non_empty_repos_file, 'w') as file:
        file.write('\n'.join(non_empty_repos))

    logging.info(f"Empty repos saved to {empty_repos_file}")
    logging.info(f"Non-empty repos saved to {non_empty_repos_file}")

    # Save full repo data
    full_data_file = f'repo_data_{timestamp}.json'
    with open(full_data_file, 'w') as file:
        json.dump(repos, file, indent=2)
    logging.info(f"Full repository data saved to {full_data_file}")

def main():
    try:
        detect_empty_repos()
    except Exception as e:
        logging.error(f"Program execution stopped due to an error: {str(e)}")
        exit(1)

if __name__ == "__main__":
    main()