#!/bin/bash

# Enable debug mode to see all commands being executed
set -x

# Set the working directory
workingdir="/Users/<USER>/coding/together/bb_repos_backup"
echo "Working directory set to: $workingdir"

# Set the Bitbucket organization
repo_org="together_digital"
echo "Bitbucket organization set to: $repo_org"

# File containing repository names
repo_list="repo_names_to_clone.txt"

# SSH user and host configuration
ssh_user="git"
bitbucket_host="bitbucket.org"

# Get the directory of the script
script_dir="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
echo "Script directory: $script_dir"

# Full path to the repo list file
repo_list_path="${script_dir}/${repo_list}"
echo "Repository list file path: $repo_list_path"

# Ensure file has a newline at the end
sed -i '' -e '$a\' "$repo_list_path"

# Count total repositories to process
total_repos=$(grep -c '[^[:space:]]' "$repo_list_path")
echo "Total repositories to process: $total_repos"

echo "Contents of repository list file:"
cat "$repo_list_path"

# Create the working directory if it doesn't exist
mkdir -p "$workingdir"

# Change to the working directory
cd "$workingdir" || exit 1

# Initialize counters
successful_clones=0
failed_clones=0

# Read repositories and process them
while IFS= read -r repo_name || [ -n "$repo_name" ]; do
    # Remove any trailing whitespace
    repo_name=$(echo "$repo_name" | tr -d '[:space:]')
    
    if [ -z "$repo_name" ]; then
        continue
    fi
    
    echo "Processing repository: $repo_name"
    
    # Construct the repository URL
    repo_url="${ssh_user}@${bitbucket_host}:${repo_org}/${repo_name}.git"
    echo "Repository URL: $repo_url"
    
    # Clone with verbose output
    echo "Attempting to clone..."
    if git clone -v --mirror "$repo_url" 2>&1; then
        echo "Successfully cloned: $repo_name"
        ((successful_clones++))
    else
        echo "Failed to clone: $repo_name"
        echo "Trying with maximum verbosity..."
        GIT_CURL_VERBOSE=1 GIT_TRACE=1 git clone -v --mirror "$repo_url" 2>&1
        ((failed_clones++))
    fi
    
    echo "------------------------"
done < "$repo_list_path"

# Print summary
echo "=== Clone Summary ==="
echo "Total repositories processed: $total_repos"
echo "Successfully cloned: $successful_clones"
echo "Failed to clone: $failed_clones"
echo "===================="

if [ "$successful_clones" -eq "$total_repos" ]; then
    echo "All repositories have been cloned successfully."
    exit 0
else
    echo "WARNING: Some repositories were not cloned successfully."
    echo "Please check the output above for errors."
    exit 1
fi