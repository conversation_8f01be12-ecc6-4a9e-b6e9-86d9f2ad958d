#!/bin/bash

# Set the working directory
workingdir="/Users/<USER>/coding/together/gh_repos_backup"

# Set the GitHub organization/user
repo_org="Together-Digital"

# File containing repository names (in the same directory as the script)
repo_list="repo_names_to_clone.txt"

# Get the directory of the script
script_dir="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Full path to the repo list file
repo_list_path="${script_dir}/${repo_list}"

# Check if the repo list file exists
if [ ! -f "$repo_list_path" ]; then
    echo "Error: Repository list file not found: $repo_list_path"
    exit 1
fi

# Create the working directory if it doesn't exist
mkdir -p "$workingdir"

# Change to the working directory
cd "$workingdir" || exit

# Read the repo list file and clone each repository
while IFS= read -r repo_name
do
    # Construct the repository HTTPS URL for GitHub
    repo_url="https://github.com/${repo_org}/${repo_name}.git"
    
    echo "Cloning repository: $repo_name"
    
    # Clone the repository with all branches
    git clone --mirror "$repo_url"
    
    # If you want a regular clone instead of a mirror, use this line instead:
    # git clone "$repo_url"
    
    echo "Finished cloning: $repo_name"
    echo "------------------------"
done < "$repo_list_path"

echo "All repositories have been cloned."