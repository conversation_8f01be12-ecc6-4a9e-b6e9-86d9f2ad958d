import os
import requests
import logging
from dotenv import load_dotenv
import time

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def handle_rate_limit(response):
    if 'X-RateLimit-Remaining' in response.headers:
        remaining = int(response.headers['X-RateLimit-Remaining'])
        if remaining <= 1:
            reset_time = int(response.headers['X-RateLimit-Reset'])
            sleep_time = reset_time - time.time() + 1
            if sleep_time > 0:
                logging.info(f"Rate limit reached. Sleeping for {sleep_time} seconds.")
                time.sleep(sleep_time)

def get_github_api(url, headers, method='GET', data=None, params=None):
    max_retries = 3
    for attempt in range(max_retries):
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers, params=params, timeout=30)
            elif method == 'PATCH':
                response = requests.patch(url, headers=headers, json=data, timeout=30)
            
            handle_rate_limit(response)
            response.raise_for_status()
            return response.json() if response.content else None
        except requests.exceptions.RequestException as e:
            if attempt == max_retries - 1:
                logging.error(f"Failed to {method} data from/to {url}: {str(e)}")
                if hasattr(e, 'response') and e.response is not None:
                    logging.error(f"Response content: {e.response.text}")
                return None
            time.sleep(2 ** attempt)  # Exponential backoff

def get_all_repos(org, headers):
    repos = []
    page = 1
    while True:
        logging.info(f"Fetching page {page} of repositories...")
        url = f"https://api.github.com/orgs/{org}/repos"
        params = {'page': page, 'per_page': 100}
        data = get_github_api(url, headers, params=params)
        if not data:
            break
        repos.extend(data)
        page += 1
        if len(data) < 100:
            break
    logging.info(f"Found {len(repos)} repositories")
    return repos

def set_default_branch(org, repo, headers):
    url = f"https://api.github.com/repos/{org}/{repo}"
    data = {"default_branch": "dev"}
    
    result = get_github_api(url, headers, method='PATCH', data=data)
    return result is not None

def main():
    github_org = os.getenv('GITHUB_ORG')
    github_token = os.getenv('GITHUB_TOKEN')

    if not all([github_org, github_token]):
        logging.error("Missing required environment variables: GITHUB_ORG and/or GITHUB_TOKEN")
        return

    headers = {
        'Authorization': f'token {github_token}',
        'Accept': 'application/vnd.github.v3+json'
    }

    repos = get_all_repos(github_org, headers)
    success_count = 0

    for repo in repos:
        repo_name = repo['name']
        default_branch = repo['default_branch']
        logging.info(f"Processing {repo_name} (current default: {default_branch})")

        # Only change if not already dev
        if default_branch != 'dev':
            # Check if dev branch exists first
            dev_branch_url = f"https://api.github.com/repos/{github_org}/{repo_name}/branches/dev"
            if get_github_api(dev_branch_url, headers):
                if set_default_branch(github_org, repo_name, headers):
                    logging.info(f"Set dev as default branch in {repo_name}")
                    success_count += 1
                else:
                    logging.error(f"Failed to set dev as default branch in {repo_name}")
            else:
                logging.warning(f"Skipping {repo_name}: dev branch does not exist")
        else:
            logging.info(f"Skipping {repo_name}: already using dev as default branch")

        time.sleep(1)  # Rate limiting precaution

    logging.info(f"Successfully processed {success_count} out of {len(repos)} repositories")

if __name__ == "__main__":
    main()