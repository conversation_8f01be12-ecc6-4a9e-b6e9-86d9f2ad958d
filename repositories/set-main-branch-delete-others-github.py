import os
import requests
import logging
from dotenv import load_dotenv
import time

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def handle_rate_limit(response):
    if 'X-RateLimit-Remaining' in response.headers:
        remaining = int(response.headers['X-RateLimit-Remaining'])
        if remaining <= 1:
            reset_time = int(response.headers['X-RateLimit-Reset'])
            sleep_time = reset_time - time.time() + 1
            if sleep_time > 0:
                logging.info(f"Rate limit reached. Sleeping for {sleep_time} seconds.")
                time.sleep(sleep_time)

def get_github_api(url, headers, method='GET', data=None, params=None):
    max_retries = 3
    for attempt in range(max_retries):
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers, params=params, timeout=30)
            elif method == 'PATCH':
                response = requests.patch(url, headers=headers, json=data, timeout=30)
            elif method == 'POST':
                response = requests.post(url, headers=headers, json=data, timeout=30)
            elif method == 'DELETE':
                response = requests.delete(url, headers=headers, timeout=30)
            else:
                raise Exception(f"Invalid HTTP method: {method}")

            handle_rate_limit(response)

            if method == 'DELETE' and response.status_code == 204:
                return True

            if response.status_code >= 400:
                logging.error(f"API error: {response.status_code} - {response.text}")
                return None

            return response.json() if response.content else True
        except requests.exceptions.RequestException as e:
            if attempt == max_retries - 1:
                logging.error(f"Failed to {method} data from/to {url}: {str(e)}")
                if hasattr(e, 'response') and e.response is not None:
                    logging.error(f"Response content: {e.response.text}")
                return None
            time.sleep(2 ** attempt)  # Exponential backoff

def get_all_branches(org, repo, headers):
    """Get all branches for a repository"""
    branches = []
    page = 1
    while True:
        url = f"https://api.github.com/repos/{org}/{repo}/branches"
        params = {'page': page, 'per_page': 100}
        data = get_github_api(url, headers, params=params)
        if not data:
            break
        branches.extend(data)
        page += 1
        if len(data) < 100:
            break
    return branches

def check_branch_exists(org, repo, branch, headers):
    """Check if a branch exists in the repository"""
    url = f"https://api.github.com/repos/{org}/{repo}/branches/{branch}"
    try:
        response = requests.get(url, headers=headers, timeout=30)
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False

def check_branch_protection(org, repo, branch, headers):
    """Check if a branch is protected"""
    url = f"https://api.github.com/repos/{org}/{repo}/branches/{branch}"
    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            branch_data = response.json()
            return branch_data.get('protected', False)
        return False
    except requests.exceptions.RequestException:
        return False

def remove_branch_protection(org, repo, branch, headers):
    """Remove protection from a branch"""
    url = f"https://api.github.com/repos/{org}/{repo}/branches/{branch}/protection"
    try:
        response = requests.delete(url, headers=headers, timeout=30)
        if response.status_code in [200, 204]:
            logging.info(f"Removed protection from branch '{branch}' in {repo}")
            return True
        else:
            logging.error(f"Failed to remove protection from branch '{branch}' in {repo}: {response.status_code} - {response.text}")
            return False
    except requests.exceptions.RequestException as e:
        logging.error(f"Error removing protection from branch '{branch}' in {repo}: {str(e)}")
        return False

def set_default_branch(org, repo, headers):
    """Set 'main' as the default branch for a repository"""
    url = f"https://api.github.com/repos/{org}/{repo}"
    data = {"default_branch": "main"}

    result = get_github_api(url, headers, method='PATCH', data=data)
    if result:
        logging.info(f"Set 'main' as default branch for {repo}")
        return True
    else:
        logging.error(f"Failed to set 'main' as default branch for {repo}")
        return False

def delete_branch(org, repo, branch, headers, force=True):
    """Delete a branch from a repository"""
    # Don't delete the 'main' branch
    if branch == 'main':
        return True

    # Check if branch is protected
    is_protected = check_branch_protection(org, repo, branch, headers)
    if is_protected:
        if not force:
            logging.warning(f"Branch '{branch}' in {repo} is protected. Skipping deletion.")
            return False
        else:
            logging.info(f"Branch '{branch}' in {repo} is protected, but force_delete=True. Attempting deletion anyway.")

    url = f"https://api.github.com/repos/{org}/{repo}/git/refs/heads/{branch}"
    result = get_github_api(url, headers, method='DELETE')
    if result:
        logging.info(f"Deleted branch '{branch}' from {repo}")
        return True
    else:
        logging.error(f"Failed to delete branch '{branch}' from {repo}")
        # Even with force=True, the API might still reject the deletion
        # This is expected for some protected branches that GitHub won't allow to be deleted
        return False

def get_repository_info(org, repo_name, headers):
    """Get repository information including the default branch"""
    url = f"https://api.github.com/repos/{org}/{repo_name}"
    return get_github_api(url, headers)

def create_main_branch(org, repo_name, source_branch, headers):
    """Create a 'main' branch from the source branch"""
    # First, get the SHA of the source branch
    url = f"https://api.github.com/repos/{org}/{repo_name}/git/refs/heads/{source_branch}"
    ref_data = get_github_api(url, headers)
    if not ref_data:
        logging.error(f"Failed to get reference for {source_branch} branch in {repo_name}")
        return False

    sha = ref_data['object']['sha']

    # Create the new 'main' branch
    url = f"https://api.github.com/repos/{org}/{repo_name}/git/refs"
    data = {
        "ref": "refs/heads/main",
        "sha": sha
    }

    result = get_github_api(url, headers, method='POST', data=data)
    if result:
        logging.info(f"Created 'main' branch from '{source_branch}' in {repo_name}")
        return True
    else:
        logging.error(f"Failed to create 'main' branch in {repo_name}")
        return False

def process_repository(org, repo_name, headers, force_delete=False, unprotect_branches=False):
    """Process a single repository: set main as default and delete other branches"""
    success = True

    # Get repository info to find the current default branch
    repo_info = get_repository_info(org, repo_name, headers)
    if not repo_info:
        logging.error(f"Failed to get repository information for {repo_name}")
        return False

    current_default_branch = repo_info['default_branch']

    # Check if 'main' branch exists
    if not check_branch_exists(org, repo_name, "main", headers):
        logging.info(f"Repository {repo_name} does not have a 'main' branch. Creating it from '{current_default_branch}'")
        if not create_main_branch(org, repo_name, current_default_branch, headers):
            return False

    # Set 'main' as the default branch
    if not set_default_branch(org, repo_name, headers):
        success = False

    # Get all branches and delete those that are not 'main'
    branches = get_all_branches(org, repo_name, headers)
    if branches:
        for branch in branches:
            branch_name = branch['name']
            if branch_name != 'main':
                # Check if branch is protected and unprotect if configured
                if unprotect_branches and check_branch_protection(org, repo_name, branch_name, headers):
                    logging.info(f"Attempting to remove protection from branch '{branch_name}' in {repo_name}")
                    remove_branch_protection(org, repo_name, branch_name, headers)

                # Try to delete the branch
                if not delete_branch(org, repo_name, branch_name, headers, force=force_delete):
                    logging.warning(f"Could not delete branch '{branch_name}' in {repo_name}")
                    success = False

    return success

def main():
    # Configuration - set defaults here
    force_delete = True  # Always try to force delete branches
    unprotect_branches = True  # Try to unprotect branches before deletion
    repos_file = 'repos_to_process.txt'  # Default file with repository names

    github_org = os.getenv('GITHUB_ORG')
    github_token = os.getenv('GITHUB_TOKEN')

    # Check if all required environment variables are set
    required_vars = ['GITHUB_ORG', 'GITHUB_TOKEN']
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        raise Exception("Missing required environment variables")

    headers = {
        'Authorization': f'token {github_token}',
        'Accept': 'application/vnd.github.v3+json'
    }

    # Read repository names from file
    try:
        with open(repos_file, 'r') as f:
            repos = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        logging.error(f"{repos_file} file not found")
        raise Exception(f"Missing {repos_file} file")

    success_count = 0
    total_repos = len(repos)

    logging.info("Running with force_delete=True - will attempt to force delete branches")
    if unprotect_branches:
        logging.info("Will attempt to remove branch protection before deletion")

    for repo_name in repos:
        logging.info(f"Processing repository: {repo_name}")

        # Process repository with full functionality and force_delete=True by default
        if process_repository(github_org, repo_name, headers,
                            force_delete=force_delete,
                            unprotect_branches=unprotect_branches):
            success_count += 1

        time.sleep(1)  # Add a small delay to avoid rate limiting

    logging.info(f"Successfully processed {success_count} out of {total_repos} repositories.")

if __name__ == "__main__":
    main()