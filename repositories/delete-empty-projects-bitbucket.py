import requests
import logging
from dotenv import load_dotenv
import os
import time
import json
import hashlib

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_bitbucket_projects(auth):
    url = "https://api.bitbucket.org/2.0/workspaces/{workspace}/projects"
    params = {"pagelen": 100}
    projects = []

    while url:
        response = requests.get(url.format(workspace=os.getenv('BITBUCKET_WORKSPACE')), auth=auth, params=params)
        if response.status_code == 200:
            data = response.json()
            projects.extend(data['values'])
            url = data.get('next')
        else:
            logging.error(f"Failed to fetch projects. Status code: {response.status_code}")
            logging.error(f"Response content: {response.text}")
            return None

    return projects

def is_project_empty(auth, workspace, project_key):
    url = f"https://api.bitbucket.org/2.0/repositories/{workspace}"
    params = {"q": f"project.key=\"{project_key}\"", "pagelen": 1}
    
    response = requests.get(url, auth=auth, params=params)
    if response.status_code == 200:
        data = response.json()
        return len(data['values']) == 0
    else:
        logging.error(f"Failed to check if project {project_key} is empty. Status code: {response.status_code}")
        logging.error(f"Response content: {response.text}")
        return False

def delete_bitbucket_project(auth, workspace, project_key):
    url = f"https://api.bitbucket.org/2.0/workspaces/{workspace}/projects/{project_key}"
    response = requests.delete(url, auth=auth)
    if response.status_code == 204:
        return True
    else:
        logging.error(f"Failed to delete project {project_key}. Status code: {response.status_code}")
        logging.error(f"Response content: {response.text}")
        return False

def get_input_hash():
    return hashlib.md5(str(time.time()).encode()).hexdigest()

def delete_empty_projects():
    # Read environment variables
    bitbucket_workspace = os.getenv('BITBUCKET_WORKSPACE')
    bitbucket_username = os.getenv('BITBUCKET_USERNAME')
    bitbucket_app_password = os.getenv('BITBUCKET_APP_PASSWORD')

    # Check if all required environment variables are set
    required_vars = ['BITBUCKET_WORKSPACE', 'BITBUCKET_USERNAME', 'BITBUCKET_APP_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        raise Exception("Missing required environment variables")

    bitbucket_auth = (bitbucket_username, bitbucket_app_password)

    projects = get_bitbucket_projects(bitbucket_auth)
    if projects is None:
        raise Exception("Failed to fetch projects")

    deleted_projects = []
    failed_projects = []

    for project in projects:
        project_key = project['key']
        if project_key == 'TD2024':
            logging.info(f"Skipping project with key TD2024")
            continue

        if is_project_empty(bitbucket_auth, bitbucket_workspace, project_key):
            try:
                if delete_bitbucket_project(bitbucket_auth, bitbucket_workspace, project_key):
                    logging.info(f"Successfully deleted empty project: {project_key}")
                    deleted_projects.append(project_key)
                else:
                    logging.warning(f"Failed to delete empty project: {project_key}")
                    failed_projects.append(project_key)
                time.sleep(1)  # Sleep for 1 second between requests
            except Exception as e:
                logging.error(f"Error deleting project {project_key}: {str(e)}")
                failed_projects.append(project_key)
                continue

    # Save results to JSON file
    results = {
        "deleted_projects": deleted_projects,
        "failed_projects": failed_projects
    }
    input_hash = get_input_hash()
    filename = f"deletion_results_{input_hash}.json"
    with open(filename, 'w') as json_file:
        json.dump(results, json_file, indent=2)
    logging.info(f"Deletion results saved to {filename}")
    return results

def main():
    try:
        results = delete_empty_projects()
        logging.info(f"Deletion process completed. Deleted: {len(results['deleted_projects'])} projects. Failed: {len(results['failed_projects'])} projects.")
    except Exception as e:
        logging.error(f"Program execution stopped due to an error: {str(e)}")
        exit(1)

if __name__ == "__main__":
    main()