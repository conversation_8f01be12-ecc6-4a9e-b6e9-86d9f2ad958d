import requests
import logging
from dotenv import load_dotenv
import os
import time
import json
import hashlib

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def delete_bitbucket_repo(workspace, repo_slug, auth):
    url = f"https://api.bitbucket.org/2.0/repositories/{workspace}/{repo_slug}"
    response = requests.delete(url, auth=auth)
    if response.status_code == 204:
        return True
    else:
        logging.error(f"Failed to delete repository {repo_slug}. Status code: {response.status_code}")
        logging.error(f"Response content: {response.text}")
        return False

def get_input_hash():
    with open('repos_to_delete.txt', 'rb') as file:
        return hashlib.md5(file.read()).hexdigest()

def delete_repos():
    # Read environment variables
    bitbucket_workspace = os.getenv('BITBUCKET_WORKSPACE')
    bitbucket_username = os.getenv('BITBUCKET_USERNAME')
    bitbucket_app_password = os.getenv('BITBUCKET_APP_PASSWORD')

    # Check if all required environment variables are set
    required_vars = ['BITBUCKET_WORKSPACE', 'BITBUCKET_USERNAME', 'BITBUCKET_APP_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        raise Exception("Missing required environment variables")

    bitbucket_auth = (bitbucket_username, bitbucket_app_password)

    with open('repos_to_delete.txt', 'r') as file:
        repo_slugs = file.read().splitlines()

    deleted_repos = []
    failed_repos = []

    for repo_slug in repo_slugs:
        try:
            if delete_bitbucket_repo(bitbucket_workspace, repo_slug, bitbucket_auth):
                logging.info(f"Successfully deleted repository: {repo_slug}")
                deleted_repos.append(repo_slug)
            else:
                logging.warning(f"Failed to delete repository: {repo_slug}")
                failed_repos.append(repo_slug)
            time.sleep(1)  # Sleep for 1 second between requests
        except Exception as e:
            logging.error(f"Error deleting repo {repo_slug}: {str(e)}")
            failed_repos.append(repo_slug)
            continue

    # Save results to JSON file
    results = {
        "deleted_repos": deleted_repos,
        "failed_repos": failed_repos
    }
    input_hash = get_input_hash()
    filename = f"deletion_results_{input_hash}.json"
    with open(filename, 'w') as json_file:
        json.dump(results, json_file, indent=2)

    logging.info(f"Deletion results saved to {filename}")
    return results

def main():
    try:
        results = delete_repos()
        logging.info(f"Deletion process completed. Deleted: {len(results['deleted_repos'])} repos. Failed: {len(results['failed_repos'])} repos.")
    except Exception as e:
        logging.error(f"Program execution stopped due to an error: {str(e)}")
        exit(1)

if __name__ == "__main__":
    main()