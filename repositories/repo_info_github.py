import os
import requests
import csv
from datetime import datetime
import time
import logging
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_github_api(url, headers, params=None):
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            if attempt == max_retries - 1:
                logging.error(f"Failed to fetch data from {url}: {str(e)}")
                return None
            time.sleep(2 ** attempt)  # Exponential backoff

def get_all_items(url, headers, item_type):
    items = []
    page = 1
    while True:
        logging.info(f"Fetching page {page} of {item_type}...")
        params = {'page': page, 'per_page': 100}
        data = get_github_api(url, headers, params)
        if not data:
            break
        items.extend(data)
        page += 1
        if len(data) < 100:
            break
    logging.info(f"Found {len(items)} {item_type}")
    return items

def get_repo_info(org, repo, headers):
    logging.info(f"Processing repository: {repo}")
    base_url = f"https://api.github.com/repos/{org}/{repo}"

    repo_info = get_github_api(base_url, headers)
    if not repo_info:
        return None

    branches = get_all_items(f"{base_url}/branches", headers, "branches")
    production_branch = next((b for b in branches if b['name'] in ['production', 'master', 'main']), None)

    if production_branch:
        branch_info = get_github_api(f"{base_url}/branches/{production_branch['name']}", headers)
        if branch_info:
            last_commit = branch_info['commit']['commit']
            last_commit_date = datetime.strptime(last_commit['author']['date'], "%Y-%m-%dT%H:%M:%SZ")
            last_commit_author = last_commit['author']['name']
        else:
            last_commit_date = last_commit_author = 'N/A'
    else:
        production_branch = {'name': 'N/A'}
        last_commit_date = last_commit_author = 'N/A'

    return {
        'repo_id': repo_info['id'],
        'project': repo_info.get('owner', {}).get('login', 'N/A'),  # Using owner as project for GitHub
        'name': repo_info['name'],
        'repo_url': repo_info['html_url'],
        'created_on': datetime.strptime(repo_info['created_at'], "%Y-%m-%dT%H:%M:%SZ"),
        'production_branch': production_branch['name'],
        'branches': ','.join(b['name'] for b in branches),
        'last_commit_date': last_commit_date,
        'last_commit_author': last_commit_author,
    }

def generate_report(org, headers, output_file):
    logging.info(f"Starting report generation for organization: {org}")

    repos_url = f"https://api.github.com/orgs/{org}/repos"
    repos = get_all_items(repos_url, headers, "repositories")

    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['Repo ID', 'Project', 'Repository', 'Repo URL', 'Created On', 'Production Branch',
                      'Branches', 'Last Commit Date', 'Last Commit Author']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for i, repo in enumerate(repos, 1):
            logging.info(f"Processing repository {i}/{len(repos)}: {repo['name']}")
            repo_info = get_repo_info(org, repo['name'], headers)
            if repo_info:
                writer.writerow({
                    'Repo ID': repo_info['repo_id'],
                    'Project': repo_info['project'],
                    'Repository': repo_info['name'],
                    'Repo URL': repo_info['repo_url'],
                    'Created On': repo_info['created_on'],
                    'Production Branch': repo_info['production_branch'],
                    'Branches': repo_info['branches'],
                    'Last Commit Date': repo_info['last_commit_date'],
                    'Last Commit Author': repo_info['last_commit_author'],
                })
            else:
                logging.warning(f"Failed to get information for repository: {repo['name']}")

            # Add a small delay to avoid rate limiting
            time.sleep(1)

    logging.info(f"Report generation completed. Output saved to {output_file}")

if __name__ == "__main__":
    github_org = os.getenv('GITHUB_ORG')
    github_token = os.getenv('GITHUB_TOKEN')

    # Check if all required environment variables are set
    required_vars = ['GITHUB_ORG', 'GITHUB_TOKEN']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        raise Exception("Missing required environment variables")

    output_file = f"github_report_{int(datetime.now().timestamp())}.csv"

    headers = {
        'Authorization': f'token {github_token}',
        'Accept': 'application/vnd.github.v3+json'
    }
    generate_report(github_org, headers, output_file)