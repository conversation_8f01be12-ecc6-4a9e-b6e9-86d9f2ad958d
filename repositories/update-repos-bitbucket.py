import requests
import logging
from dotenv import load_dotenv
import os
import time

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_branches(workspace, repo_slug, auth):
    url = f"https://api.bitbucket.org/2.0/repositories/{workspace}/{repo_slug}/refs/branches"
    response = requests.get(url, auth=auth)
    if response.status_code == 200:
        return [branch['name'] for branch in response.json()['values']]
    else:
        logging.error(f"Failed to get branches for {repo_slug}. Status code: {response.status_code}")
        return None

def set_main_branch(workspace, repo_slug, branch_name, auth):
    url = f"https://api.bitbucket.org/2.0/repositories/{workspace}/{repo_slug}"
    data = {"mainbranch": {"name": branch_name}}
    response = requests.put(url, json=data, auth=auth)
    if response.status_code == 200:
        logging.info(f"Set main branch to '{branch_name}' for {repo_slug}")
        return True
    else:
        logging.error(f"Failed to set main branch for {repo_slug}. Status code: {response.status_code}")
        return False

def rename_branch(workspace, repo_slug, old_name, new_name, auth):
    url = f"https://api.bitbucket.org/2.0/repositories/{workspace}/{repo_slug}/refs/branches/{old_name}"
    data = {"name": new_name}
    response = requests.post(url, json=data, auth=auth)
    if response.status_code == 201:
        logging.info(f"Renamed branch '{old_name}' to '{new_name}' for {repo_slug}")
        return True
    else:
        logging.error(f"Failed to rename branch for {repo_slug}. Status code: {response.status_code}")
        return False

def process_repo(workspace, repo_slug, auth):
    try:
        branches = get_branches(workspace, repo_slug, auth)
        if branches is None:
            return

        if "master" in branches and "production" in branches:
            logging.info(f"master and production branch overlap for {repo_slug}")

        if "dev" in branches:
            set_main_branch(workspace, repo_slug, "dev", auth)
        elif "development" in branches:
            if rename_branch(workspace, repo_slug, "development", "dev", auth):
                set_main_branch(workspace, repo_slug, "dev", auth)
        else:
            logging.info(f"no dev or development branch found for {repo_slug}")
    except Exception as e:
        logging.error(f"Error processing repository {repo_slug}: {str(e)}")

def get_all_repos(workspace, auth):
    url = f"https://api.bitbucket.org/2.0/repositories/{workspace}"
    repos = []
    while url:
        response = requests.get(url, auth=auth)
        if response.status_code != 200:
            logging.error(f"Failed to get repositories. Status code: {response.status_code}")
            return None
        data = response.json()
        repos.extend(data['values'])
        url = data.get('next')
    return repos

def main():
    # Read environment variables
    workspace = os.getenv('BITBUCKET_WORKSPACE')
    username = os.getenv('BITBUCKET_USERNAME')
    app_password = os.getenv('BITBUCKET_APP_PASSWORD')

    # Check if all required environment variables are set
    required_vars = ['BITBUCKET_WORKSPACE', 'BITBUCKET_USERNAME', 'BITBUCKET_APP_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        raise Exception("Missing required environment variables")

    # Ensure both username and app_password are not None before creating the auth tuple
    if username is None or app_password is None:
        logging.error("Username or app password is None")
        raise Exception("Invalid credentials")

    auth = (username, app_password)

    repos = get_all_repos(workspace, auth)
    if repos is None:
        return

    for repo in repos:
        repo_slug = repo['slug']
        process_repo(workspace, repo_slug, auth)
        time.sleep(2)  # Increased sleep time to 2 seconds

if __name__ == "__main__":
    main()