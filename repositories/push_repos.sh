#!/bin/bash

# Set the working directory
workingdir="/Users/<USER>/coding/together/bb_repos_backup"

# Set the Bitbucket organization
repo_org="Together-Digital"

# File containing repository names (in the same directory as the script)
repo_list="repo_names_to_push.txt"

# New remote name
new_remote="new_remote"

# Read each line from the input file
while IFS= read -r repo_name
do
    # Construct the repo SSH URL
    repo_url="**************:${repo_org}/${repo_name}.git"

    dir_path="$workingdir/$repo_name.git"
    
    # Check if the local directory exists
    if [ -d $dir_path ]; then
        echo "Processing repository: $repo_name"
        
        # Change to the repository directory
        cd $dir_path
        
        # Add the new remote
        git remote add $new_remote $repo_url
        
        git push --mirror $new_remote
        
        echo "Finished processing $repo_name"
        echo "------------------------"
    else
        echo "Directory $dir_path does not exist. Skipping."
        echo "------------------------"
    fi
done < "$repo_list"

echo "All repositories processed."