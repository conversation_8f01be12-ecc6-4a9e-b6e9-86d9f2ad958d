import os
import requests
import logging
from dotenv import load_dotenv
import time

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def handle_rate_limit(response):
    if 'X-RateLimit-Remaining' in response.headers:
        remaining = int(response.headers['X-RateLimit-Remaining'])
        if remaining <= 1:
            reset_time = int(response.headers['X-RateLimit-Reset'])
            sleep_time = reset_time - time.time() + 1
            if sleep_time > 0:
                logging.info(f"Rate limit reached. Sleeping for {sleep_time} seconds.")
                time.sleep(sleep_time)

def get_github_api(url, headers, method='GET', data=None, params=None):
    max_retries = 3
    for attempt in range(max_retries):
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers, params=params, timeout=30)
            elif method == 'PUT':
                response = requests.put(url, headers=headers, json=data, timeout=30)
            else:
                raise Exception(f"Invalid HTTP method: {method}")
            
            handle_rate_limit(response)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            if attempt == max_retries - 1:
                logging.error(f"Failed to {method} data from/to {url}: {str(e)}")
                if hasattr(e, 'response') and e.response is not None:
                    logging.error(f"Response content: {e.response.text}")
                return None
            time.sleep(2 ** attempt)  # Exponential backoff

def get_all_repos(org, headers):
    repos = []
    page = 1
    while True:
        logging.info(f"Fetching page {page} of repositories...")
        url = f"https://api.github.com/orgs/{org}/repos"
        params = {'page': page, 'per_page': 100}
        data = get_github_api(url, headers, params=params)
        if not data:
            break
        repos.extend(data)
        page += 1
        if len(data) < 100:
            break
    logging.info(f"Found {len(repos)} repositories")
    return repos

def get_repo_status_checks(org, repo, branch, headers):
    """Get available status checks for a repository branch"""
    url = f"https://api.github.com/repos/{org}/{repo}/branches/{branch}/protection/required_status_checks/contexts"
    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            return response.json()
    except requests.exceptions.RequestException as e:
        logging.warning(f"Could not fetch status checks for {repo}/{branch}: {e}")
    return []

def get_branch_protection_rules():
    """Return the standard branch protection rules"""
    return {
        "required_status_checks": {
            "strict": True,
            "contexts": [],  # Will be populated with actual status checks
        },
        "enforce_admins": True,
        "required_pull_request_reviews": {
            "dismiss_stale_reviews": False,
            "require_code_owner_reviews": False,
            "required_approving_review_count": 0,
            "require_last_push_approval": False
        },
        "restrictions": None,
        "required_linear_history": False,
        "allow_force_pushes": False,
        "allow_deletions": False,
        "required_conversation_resolution": True,
        "lock_branch": False,
        "allow_fork_syncing": False,
    }

def set_branch_protection(org, repo, branch, headers):
    # First, get available status checks
    status_checks = get_repo_status_checks(org, repo, branch, headers)
    
    protection_data = get_branch_protection_rules()
    protection_data["required_status_checks"]["contexts"] = status_checks
    
    url = f"https://api.github.com/repos/{org}/{repo}/branches/{branch}/protection"
    
    try:
        response = requests.put(url, headers=headers, json=protection_data, timeout=30)
        handle_rate_limit(response)
        response.raise_for_status()
        logging.info(f"Branch protection rules set for {repo}/{branch}")
        return True
    except requests.exceptions.HTTPError as e:
        logging.error(f"HTTP error occurred while setting branch protection for {repo}/{branch}: {e}")
        logging.error(f"Response content: {e.response.text}")
    except requests.exceptions.RequestException as e:
        logging.error(f"Error occurred while setting branch protection for {repo}/{branch}: {e}")
    return False

def check_branch_exists(org, repo, branch, headers):
    """Check if a branch exists in the repository"""
    url = f"https://api.github.com/repos/{org}/{repo}/branches/{branch}"
    try:
        response = requests.get(url, headers=headers, timeout=30)
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False

def process_repository(org, repo_name, headers, branches=None):
    """Process a single repository for multiple branches"""
    if branches is None:
        branches = ["main", "dev"]
    
    success = 0
    for branch in branches:
        if check_branch_exists(org, repo_name, branch, headers):
            if set_branch_protection(org, repo_name, branch, headers):
                success += 1
        else:
            logging.info(f"Branch {branch} does not exist in repository {repo_name}")
    
    return success

def main():
    github_org = os.getenv('GITHUB_ORG')
    github_token = os.getenv('GITHUB_TOKEN')

    # Check if all required environment variables are set
    required_vars = ['GITHUB_ORG', 'GITHUB_TOKEN']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logging.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        raise Exception("Missing required environment variables")

    headers = {
        'Authorization': f'token {github_token}',
        'Accept': 'application/vnd.github.v3+json'
    }

    repos = get_all_repos(github_org, headers)
    
    success_count = 0
    total_possible = len(repos) * 2  # Two branches per repo

    for repo in repos:
        repo_name = repo['name']
        logging.info(f"Processing repository: {repo_name}")
        success_count += process_repository(github_org, repo_name, headers)
        time.sleep(1)  # Add a small delay to avoid rate limiting

    logging.info(f"Branch protection rules set for {success_count} out of {total_possible} possible branches.")

if __name__ == "__main__":
    main()