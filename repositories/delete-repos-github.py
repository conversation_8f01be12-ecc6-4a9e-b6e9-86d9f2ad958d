import requests
import logging
from getpass import getpass

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def delete_github_repo(org, repo_name, headers):
    url = f"https://api.github.com/repos/{org}/{repo_name}"
    response = requests.delete(url, headers=headers)
    if response.status_code == 204:
        logging.info(f"Successfully deleted repository: {repo_name}")
    else:
        logging.error(f"Failed to delete repository {repo_name}. Status code: {response.status_code}")
        logging.error(f"Response: {response.text}")

def delete_empty_repos():
    # Get user input for GitHub organization and token
    github_org = input("Enter GitHub organization name: ").strip()
    github_token = getpass("Enter GitHub personal access token: ").strip()

    if not github_org or not github_token:
        logging.error("GitHub organization and token are required")
        raise Exception("Missing required inputs")

    github_headers = {
        'Authorization': f'token {github_token}',
        'Accept': 'application/vnd.github.v3+json'
    }

    # Read the list of empty repositories from the file
    try:
        with open('repos_to_delete.txt', 'r') as file:
            repos_to_delete = file.read().splitlines()
    except FileNotFoundError:
        logging.error("repos_to_delete.txt file not found")
        raise

    logging.info(f"Found {len(repos_to_delete)} repositories to delete")
    
    # Confirm deletion
    confirmation = input(f"Are you sure you want to delete {len(repos_to_delete)} repositories? (yes/no): ").lower()
    if confirmation != 'yes':
        logging.info("Operation cancelled by user")
        return

    for repo_name in repos_to_delete:
        delete_github_repo(github_org, repo_name, github_headers)

if __name__ == "__main__":
    delete_empty_repos()