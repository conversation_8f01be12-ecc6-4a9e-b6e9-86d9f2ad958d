#!/bin/bash

ORG_NAME="Together-Digital"
BRANCH_NAME="fix/storyblok-bridge-save-issue"
COMMIT_MSG="fix: storyblok-bridge-save-issue"
WORKING_DIR="repo-working-dir"
REPOS_FILE="repositories.txt"

# Check if repositories file exists
if [ ! -f "$REPOS_FILE" ]; then
    echo "Error: repositories.txt file not found"
    exit 1
fi

# Create working directory if it doesn't exist
mkdir -p "$WORKING_DIR"

# Read repositories file line by line
while IFS= read -r repo_name; do
    # Skip empty lines and comments
    [[ -z "$repo_name" || "$repo_name" =~ ^#.*$ ]] && continue
    
    # Construct full repository URL
    repo="https://github.com/${ORG_NAME}/${repo_name}.git"
    echo "Processing repository: $repo"
    
    # Use repo_name for directory
    REPO_PATH="$WORKING_DIR/$repo_name"
    
    # Clone repository
    git clone "$repo" "$REPO_PATH"
    cd "$REPO_PATH" || exit 1
    
    # Create and checkout new branch
    git checkout -b "$BRANCH_NAME"
    
    
    # Copy Storyblok bridge file
    cp ../../files/storyblok.html snippets/
    
    # Stage files
    git add snippets/storyblok.html
    
    # Commit changes
    git commit -m "$COMMIT_MSG"
    
    # Push changes
    git push origin "$BRANCH_NAME"
    
    # Go back and clean up
    cd "$OLDPWD"
    rm -rf "$REPO_PATH"
    
    echo "Completed processing: $repo"
    echo "-----------------------------------"
done < "$REPOS_FILE"

# Clean up working directory if empty
rmdir "$WORKING_DIR" 2>/dev/null || true

echo "All repositories processed successfully!"
