import os
import requests
import logging
from dotenv import load_dotenv
import time

load_dotenv()
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def handle_rate_limit(response):
    if 'X-RateLimit-Remaining' in response.headers:
        remaining = int(response.headers['X-RateLimit-Remaining'])
        if remaining <= 1:
            reset_time = int(response.headers['X-RateLimit-Reset'])
            sleep_time = reset_time - time.time() + 1
            if sleep_time > 0:
                logging.info(f"Rate limit reached. Sleeping for {sleep_time} seconds.")
                time.sleep(sleep_time)

def get_github_api(url, headers, method='GET', data=None):
    max_retries = 3
    for attempt in range(max_retries):
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers, timeout=30)
            elif method == 'POST':
                response = requests.post(url, headers=headers, json=data, timeout=30)
            
            handle_rate_limit(response)
            response.raise_for_status()
            return response.json() if response.content else None
        except requests.exceptions.RequestException as e:
            if attempt == max_retries - 1:
                logging.error(f"Failed to {method} data from/to {url}: {str(e)}")
                if hasattr(e, 'response') and e.response is not None:
                    logging.error(f"Response content: {e.response.text}")
                return None
            time.sleep(2 ** attempt)

def rename_branch(org, repo, headers):
    # Get the production branch reference
    ref_url = f"https://api.github.com/repos/{org}/{repo}/git/refs/heads/production"
    production_ref = get_github_api(ref_url, headers)
    
    if not production_ref:
        logging.error(f"Production branch not found in {repo}")
        return False
    
    # Create new branch 'main' pointing to the same commit
    create_ref_url = f"https://api.github.com/repos/{org}/{repo}/git/refs"
    create_data = {
        "ref": "refs/heads/main",
        "sha": production_ref['object']['sha']
    }
    
    if not get_github_api(create_ref_url, headers, method='POST', data=create_data):
        logging.error(f"Failed to create main branch in {repo}")
        return False
    
    # Delete production branch
    delete_url = f"https://api.github.com/repos/{org}/{repo}/git/refs/heads/production"
    try:
        response = requests.delete(delete_url, headers=headers)
        response.raise_for_status()
        return True
    except requests.exceptions.RequestException as e:
        logging.error(f"Failed to delete production branch in {repo}: {str(e)}")
        return False

def main():
    github_org = os.getenv('GITHUB_ORG')
    github_token = os.getenv('GITHUB_TOKEN')

    if not all([github_org, github_token]):
        logging.error("Missing required environment variables: GITHUB_ORG and/or GITHUB_TOKEN")
        return

    try:
        with open('repos_to_rename_branch.txt', 'r') as f:
            repos = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        logging.error("repos_to_rename_branch.txt file not found")
        return

    headers = {
        'Authorization': f'token {github_token}',
        'Accept': 'application/vnd.github.v3+json'
    }

    success_count = 0

    for repo in repos:
        logging.info(f"Processing {repo}")
        
        if rename_branch(github_org, repo, headers):
            logging.info(f"Successfully renamed branch in {repo}")
            success_count += 1
        else:
            logging.error(f"Failed to rename branch in {repo}")

        time.sleep(1)  # Rate limiting precaution

    logging.info(f"Successfully processed {success_count} out of {len(repos)} repositories")

if __name__ == "__main__":
    main()