
/* 

USAGE: 
1.
 run 'npm install'

2.
get the unix values for the start and end date of the period you want to get the balance change summary report for and add the values to the variables below:
startDate 
endDate
3.
run ' node getAccountBalance'


NOTE: 
USE LIVE KEY FOR REAL BALANCES AND ALSO TO DOWNLOAD THE FILES ATTACHED IN THE RESPONSE URL

*/


const stripe = require('stripe')('sk_live_example_example', {
    apiVersion: '2024-06-20',
  });


// Function to create and retrieve the balance change from activity summary report for August
async function runBalanceChangeSummaryReport() {
    try {


     // ADD UNIX VALUES OF THE DATE
      const startDate = ''
      const endDate = ''
  
      // Create the balance change from activity summary report
      const reportRun = await stripe.reporting.reportRuns.create({
        report_type: 'balance.summary.1',
        parameters: {
          interval_start: startDate.toString(), 
          interval_end: endDate.toString(),           
        },
      });
  
      console.log(`Report created with ID: ${reportRun.id}. Waiting for it to complete...`);
  

      let reportStatus = reportRun.status;
      while (reportStatus !== 'succeeded') {
        console.log(`Report status: ${reportStatus}. Checking again in 5 seconds...`);
        await new Promise(resolve => setTimeout(resolve, 5000));
  
        const updatedReportRun = await stripe.reporting.reportRuns.retrieve(reportRun.id);
        reportStatus = updatedReportRun.status;
      }
  

      const finalReport = await stripe.reporting.reportRuns.retrieve(reportRun.id);
      if (finalReport.result.object === 'file') {
        console.log(`Report is ready! Download it here: ${finalReport.result.url}`);
      } else {
        console.log('Something went wrong, no file URL available.');
      }
  
      return finalReport;
    } catch (error) {
      console.error('Error creating or fetching the report:', error);
    }
  }

  runBalanceChangeSummaryReport();