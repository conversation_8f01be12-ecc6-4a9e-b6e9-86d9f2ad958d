

const stripe = require('stripe')('sk_live example', {
    apiVersion: '2024-06-20',
  });


  const fs = require('fs');
  const path = require('path');
  const { Parser } = require('json2csv'); 
  

  function getStartOfDayTimestamp(dateString) {
      const [day, month, year] = dateString.split('/').map(Number);
      return Math.floor(new Date(year, month - 1, day).setHours(0, 0, 0, 0) / 1000);  
  }
  

  function getEndOfDayTimestamp(dateString) {
      const [day, month, year] = dateString.split('/').map(Number);
      return Math.floor(new Date(year, month - 1, day).setHours(23, 59, 59, 999) / 1000);  
  }
  

  async function getLastFewPayments(dateStr, limit = 5) {
      try {
          const startOfDay = getStartOfDayTimestamp(dateStr);
          const endOfDay = getEndOfDayTimestamp(dateStr);
  
          console.log(`Fetching charges from ${new Date(startOfDay * 1000)} to ${new Date(endOfDay * 1000)}`);
  
          // Fetch the charges for the specific day
          const charges = await stripe.charges.list({
              created: {
                  gte: startOfDay, 
                  lte: endOfDay,   
              },
              limit: limit,       
              expand: ['data.balance_transaction'], 
          });
  
          console.log(`Found ${charges.data.length} charges on ${dateStr}:`);
          
          // Prepare the data for CSV export
          const chargeData = charges.data.map((charge, index) => {
              return {
                  'Charge ID': charge.id,
                  'Amount': charge.amount / 100, // Convert cents to dollars/euros
                  'Currency': charge.currency.toUpperCase(),
                  'Name': charge.billing_details.name || 'No name available',
                  'Email': charge.billing_details.email || 'No email available',
                  'Metadata': JSON.stringify(charge.metadata),
                  'Created': new Date(charge.created * 1000).toISOString(), // Convert Unix to human-readable date
              };
          });
  
          // Define CSV fields
          const fields = ['Charge ID', 'Amount', 'Currency', 'Name', 'Email', 'Metadata', 'Created'];
          const json2csvParser = new Parser({ fields });
          const csv = json2csvParser.parse(chargeData);
  
          // Write the CSV to a file
          const filePath = path.join(__dirname, `payments_${dateStr.replace(/\//g, '-')}.csv`);
          fs.writeFileSync(filePath, csv);
  
          console.log(`CSV file successfully written to ${filePath}`);
          
          return chargeData;
  
      } catch (error) {
          console.error('Error fetching charges or writing to CSV:', error);
      }
  }
  
  // Usage: Set the date you want to retrieve the last few payments for (in 'DD/MM/YYYY' format)
  const date = '23/09/2024'; 
  const limit = 50;         
  
  getLastFewPayments(date, limit);