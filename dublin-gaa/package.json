{"name": "xml-to-storyblok", "version": "1.0.0", "scripts": {"start": "bun run src/index.ts", "xml-to-json": "bun run src/convert-xml-to-json.ts", "json-to-sb": "bun run src/convert-to-storyblok.ts", "import-to-sb": "bun run src/import-to-storyblok.ts"}, "dependencies": {"bun": "latest", "storyblok-js-client": "^6.10.7", "storyblok-markdown-richtext": "latest", "turndown": "latest", "xml2js": "latest"}, "devDependencies": {"@types/bun": "^1.2.1", "@types/turndown": "latest", "@types/xml2js": "latest"}}