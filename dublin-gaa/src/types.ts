import { ISbContentMangmntAPI } from "storyblok-js-client";

export interface BlocksNewsBannerStoryblok {
	heading?: string;
	image?: AssetStoryblok;
	component: "blocks/news-banner";
	_uid?: string;
	[k: string]: any;
}

export interface ContentTextStoryblok {
	content?: RichtextStoryblok;
	align?: "left" | "center" | "right";
	component: "content/text";
	_uid?: string;
	[k: string]: any;
}

export interface RichtextStoryblok {
	type: string;
	content?: RichtextStoryblok[];
	marks?: RichtextStoryblok[];
	attrs?: any;
	text?: string;
	[k: string]: any;
}

export interface AssetStoryblok {
	alt: string | null;
	copyright?: string | null;
	fieldtype: "asset";
	id?: number;
	filename: string | null;
	name: string;
	title: string | null;
	focus?: string | null;
	meta_data?: {
		[k: string]: any;
	};
	source?: string | null;
	is_external_url?: boolean;
	is_private?: boolean;
	src?: string;
	updated_at?: string;
	width?: number | null;
	height?: number | null;
	aspect_ratio?: number | null;
	public_id?: string | null;
	content_type?: string;
	[k: string]: any;
}

export interface BlocksContentAreaStoryblok {
	content?: ContentTextStoryblok[];
	component: "blocks/content-area";
	_uid?: string;
	[k: string]: any;
}

export interface ModulesNewsStoryblok {
	date: string;
	author?: string;
	sport_field: number | string;
	category?: number | string;
	content?: (BlocksContentAreaStoryblok | BlocksNewsBannerStoryblok)[];
	image?: AssetStoryblok;
	excerpt?: string;
	prevent_indexing?: boolean;
	component: "modules/news";
	_uid?: string;
	[k: string]: any;
}

export type CreateNewsStoryDTO = ISbContentMangmntAPI<ModulesNewsStoryblok>;
