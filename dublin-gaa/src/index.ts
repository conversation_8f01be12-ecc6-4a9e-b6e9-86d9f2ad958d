import { mkdir } from "fs/promises";
import { dirname } from "path";
import { logger } from "./utils/logger";
import { convertXmlToJson } from "./services/xml-converter";
import { convertToStoryblok } from "./services/story-converter";
import { importToStoryblok } from "./services/storyblok-importer";

const PATHS = {
	input: "data/full.xml",
	jsonOutput: "data/processed.json",
	storyblokOutput: "data/storyblok.json",
} as const;

const ensureDirectories = async () => {
	for (const path of Object.values(PATHS)) {
		await mkdir(dirname(path), { recursive: true });
	}
};

const main = async () => {
	try {
		logger.info("Starting Dublin GAA content migration");
		await ensureDirectories();

		// Step 1: Convert XML to JSON
		logger.info("\n=== Step 1: Converting XML to JSON ===");
		await convertXmlToJson(PATHS.input, PATHS.jsonOutput);

		// Step 2: Convert JSON to Storyblok format
		logger.info("\n=== Step 2: Converting to Storyblok format ===");
		await convertToStoryblok(PATHS.jsonOutput, PATHS.storyblokOutput);

		// Step 3: Import to Storyblok
		logger.info("\n=== Step 3: Importing to Storyblok ===");
		const result = await importToStoryblok(PATHS.storyblokOutput);

		logger.success("\n=== Migration Complete ===");
		logger.info(`Total stories imported: ${result.successCount}`);
		if (result.failureCount > 0) {
			logger.warn(`Failed imports: ${result.failureCount}`);
		}
	} catch (error) {
		logger.error("\n=== Migration Failed ===");
		logger.error(JSON.stringify(error, null, 2));
		process.exit(1);
	}
};

main();
