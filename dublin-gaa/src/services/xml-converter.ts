import xml2js from "xml2js";
import TurndownService from "turndown";
import { markdownToRichtext } from "storyblok-markdown-richtext";
import { RichtextStoryblok } from "../types";
import { logger } from "../utils/logger";

export interface NewsEntry {
	entry_id: string;
	title: string;
	url_title: string;
	entry_date: string;
	content: RichtextStoryblok[];
	categories: string;
	image?: {
		url: string;
		width: number;
		height: number;
		filename: string;
		mime: string;
	};
}

const processEntry = async (xmlContent: string): Promise<NewsEntry[]> => {
	const parser = new xml2js.Parser();
	const result = await parser.parseStringPromise(xmlContent);
	const entries = result.resultset.row;

	return entries.map((entry: any) => {
		const getField = (name: string) =>
			entry.field.find((f: any) => f.$.name === name)?._;

		const rawContent = getField("content") || "";
		const turndownService = new TurndownService();
		const markdownContent = turndownService.turndown(rawContent);
		const richtextContent = markdownToRichtext(markdownContent);

		const content = Array.isArray(richtextContent)
			? richtextContent
			: [
					{
						type: "doc",
						content: Array.isArray(richtextContent.content)
							? richtextContent.content
							: [
									{
										type: "paragraph",
										content: [
											{
												type: "text",
												text: markdownContent,
											},
										],
									},
							  ],
					},
			  ];

		let image;
		const base64Image = getField("base64_image_object");
		if (base64Image) {
			try {
				const decodedStr = Buffer.from(base64Image, "base64").toString();
				const getValue = (key: string): string => {
					const match = decodedStr.match(
						new RegExp(`s:\\d+:"${key}";(?:s:\\d+:"|d:)(.*?)";`)
					);
					return match ? match[1] : "";
				};

				image = {
					url: getValue("url"),
					width: Number(getValue("image_width")),
					height: Number(getValue("image_height")),
					filename: getValue("filename"),
					mime: getValue("mime"),
				};
			} catch (error) {
				logger.warn(`Failed to parse image data: ${error}`);
			}
		}

		return {
			entry_id: getField("entry_id"),
			title: getField("title"),
			url_title: getField("url_title"),
			entry_date: getField("entry_date"),
			content,
			categories: getField("categories"),
			...(image && { image }),
		};
	});
};

const validateEntry = (entry: NewsEntry) => {
	if (!entry.entry_id) throw new Error("Entry ID is missing");
	if (!entry.title) throw new Error("Title is missing");
	if (!entry.url_title) throw new Error("URL title is missing");
	if (!entry.entry_date) throw new Error("Entry date is missing");
	if (!Array.isArray(entry.content))
		throw new Error("Content should be richtext array");
	if (!entry.content.length) throw new Error("Content should not be empty");

	entry.content.forEach((block: RichtextStoryblok) => {
		if (!block.type) throw new Error("Richtext block missing type");
		if (block.type === "paragraph" && !block.content) {
			throw new Error("Paragraph block missing content");
		}
	});
};

export const convertXmlToJson = async (
	inputPath: string,
	outputPath: string
) => {
	try {
		logger.info("Starting XML to JSON conversion...");
		const xmlContent = await Bun.file(inputPath).text();
		const entries = await processEntry(xmlContent);

		logger.info(`Validating ${entries.length} entries...`);
		entries.forEach((entry, index) => {
			try {
				validateEntry(entry);
			} catch (error) {
				throw new Error(
					`Entry ${index + 1} validation failed: ${(error as Error).message}`
				);
			}
		});

		await Bun.write(outputPath, JSON.stringify(entries, null, 2));
		logger.success(`Processed ${entries.length} entries to ${outputPath}`);

		return entries;
	} catch (error) {
		logger.error(error as Error);
		throw error;
	}
};
