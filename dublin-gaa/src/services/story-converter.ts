import {
	ModulesNewsStoryblok,
	BlocksNewsBannerStoryblok,
	BlocksContentAreaStoryblok,
	ContentTextStoryblok,
	RichtextStoryblok,
	CreateNewsStoryDTO,
} from "../types";
import { NewsEntry } from "./xml-converter";
import { logger } from "../utils/logger";

const NEWS_CATEGORIES = [
	"All News",
	"General",
	"Club",
	"Inter-County",
	"Features",
] as const;

const SPORTS_FIELD_ALL = "All Sports";

/**
 * Maps a category string to its corresponding value
 * Falls back to 'All News' category if the provided category doesn't match
 */
const mapCategory = (category?: string): string => {
	const normalizedCategory = category?.trim() ?? "All News";
	return NEWS_CATEGORIES.includes(
		normalizedCategory as (typeof NEWS_CATEGORIES)[number]
	)
		? normalizedCategory
		: "All News";
};

/**
 * Converts content blocks to Storyblok's richtext format
 * Handles nested content and mark structures required by Storyblok
 */
const convertRichText = (content: any[] | undefined): RichtextStoryblok => ({
	type: "doc",
	content: (content ?? []).map((block) => ({
		...block,
		content: block.content?.map((item) => ({
			...item,
			marks: item.marks?.map((mark) => ({ ...mark })),
		})),
	})),
});

/**
 * Transforms a news entry into Storyblok's content structure
 * Creates a news module with banner and content area components
 * Handles image assets, date formatting, and category mapping
 */
const processEntry = (entry: NewsEntry): CreateNewsStoryDTO => {
	const banner: BlocksNewsBannerStoryblok = {
		component: "blocks/news-banner",
	};

	const contentArea: BlocksContentAreaStoryblok = {
		component: "blocks/content-area",
		content: (entry.content ?? []).map(
			(contentBlock): ContentTextStoryblok => ({
				component: "content/text",
				content: convertRichText(contentBlock.content),
				align: "left",
			})
		),
	};

	const mappedCategory = mapCategory(entry.categories);
	logger.info(`Mapping category: ${entry.categories} -> ${mappedCategory}`);

	return {
		story: {
			name: entry.title,
			slug: entry.url_title,
			content: {
				component: "modules/news",
				date: new Date(parseInt(entry.entry_date) * 1000)
					.toISOString()
					.split("T")[0],
				sport_field: SPORTS_FIELD_ALL,
				category: mappedCategory,
				content: [banner, contentArea],
				...(entry.image && {
					image: {
						fieldtype: "asset",
						filename: entry.image.url,
						name: entry.image.filename,
						alt: entry.image.filename,
						title: entry.image.filename,
						is_external_url: true,
						width: entry.image.width,
						height: entry.image.height,
					},
				}),
				excerpt:
					entry.content?.[0]?.content?.[1]?.content?.[0]?.text?.slice(0, 200) ??
					"",
			},
			is_startpage: false,
		},
	};
};

/**
 * Main conversion function that processes JSON input into Storyblok-compatible format
 * Reads from input JSON file, converts all entries, and writes to output JSON file
 * Includes progress logging for monitoring conversion status
 */
export const convertToStoryblok = async (
	inputPath: string,
	outputPath: string
) => {
	try {
		logger.info("Starting conversion to Storyblok format...");
		const data = JSON.parse(await Bun.file(inputPath).text());

		logger.info(`Converting ${data.length} articles...`);
		const stories = data.map((entry: NewsEntry, index: number) => {
			logger.progress(index + 1, data.length, `Processing: ${entry.title}`);
			return processEntry(entry);
		});

		await Bun.write(outputPath, JSON.stringify(stories, null, 2));
		logger.success(`Converted ${stories.length} articles to ${outputPath}`);

		return stories;
	} catch (error) {
		logger.error(error as Error);
		throw error;
	}
};
