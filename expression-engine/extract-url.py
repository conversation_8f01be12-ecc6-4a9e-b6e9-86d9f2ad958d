with open('decoded.txt', 'r') as input_file:
    with open('urls.txt', 'w') as output_file:
        for line in input_file:
            # Find the position of "url";s: in the line
            if '"url";s:' in line:
                start = line.find('"url";s:')
                # Find the http after that position
                start = line.find('http', start)
                # Find the closing quote after the http
                end = line.find('"', start)
                # Extract the URL
                url = line[start:end]
                output_file.write(url + '\n')

print("Done! Check urls.txt")