import base64

# Read and decode
with open('input.txt', 'r') as input_file:
    with open('decoded.txt', 'w') as output_file:
        for line in input_file:
            try:
                decoded = base64.b64decode(line.strip()).decode('utf-8')
                output_file.write(decoded + '\n')
            except:
                print(f"Error decoding line: {line.strip()}")

print("Decoding complete! Check decoded.txt for results")