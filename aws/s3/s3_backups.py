import os
import subprocess
import csv
from datetime import datetime

def get_aws_credentials():
    aws_access_key_id = input("Enter your AWS Access Key ID: ")
    aws_secret_access_key = input("Enter your AWS Secret Access Key: ")
    return aws_access_key_id, aws_secret_access_key

# Set variables
INPUT_FILE = "buckets_to_backup.txt"  # File containing source bucket names, one per line
DESTINATION_BUCKET = "backups-legacy-s3-buckets"  # Name of the destination backup bucket
OUTPUT_FILE = "output.csv"  # Name of the output CSV file

# Get AWS credentials
aws_access_key_id, aws_secret_access_key = get_aws_credentials()

# Check if input file exists
if not os.path.isfile(INPUT_FILE):
    print(f"Error: Input file {INPUT_FILE} not found")
    exit(1)

# Prepare CSV file
with open(OUTPUT_FILE, 'w', newline='') as csvfile:
    csvwriter = csv.writer(csvfile)
    csvwriter.writerow(['bucket', 'destination', 'size', 'time', 'errors', 'warnings', 'success'])

    # Read source buckets from input file and perform backup
    with open(INPUT_FILE, 'r') as file:
        for SOURCE_BUCKET in file:
            # Trim whitespace
            SOURCE_BUCKET = SOURCE_BUCKET.strip()
            # Skip empty lines
            if not SOURCE_BUCKET:
                continue

            print(f"Backing up {SOURCE_BUCKET} to {DESTINATION_BUCKET}/{SOURCE_BUCKET}/")

            # Get start time
            start_time = datetime.now()

            # Sync the contents of the source bucket to a folder in the destination bucket
            command = f"AWS_ACCESS_KEY_ID={aws_access_key_id} AWS_SECRET_ACCESS_KEY={aws_secret_access_key} aws s3 sync s3://{SOURCE_BUCKET} s3://{DESTINATION_BUCKET}/{SOURCE_BUCKET}/ --delete"
            result = subprocess.run(command, shell=True, capture_output=True, text=True)

            # Calculate time taken
            end_time = datetime.now()
            time_taken = (end_time - start_time).total_seconds()

            # Parse output for size, errors, and warnings
            size = "N/A"
            errors = 0
            warnings = 0
            success = result.returncode == 0

            for line in result.stdout.split('\n'):
                if "Total Objects:" in line:
                    size = line.split("Total Size:")[1].strip()
                if "Error:" in line:
                    errors += 1
                if "Warning:" in line:
                    warnings += 1

            # Write to CSV
            csvwriter.writerow([SOURCE_BUCKET, f"{DESTINATION_BUCKET}/{SOURCE_BUCKET}/", size, time_taken, errors, warnings, success])

            if success:
                print(f"Backup of {SOURCE_BUCKET} completed successfully")
            else:
                print(f"Error backing up {SOURCE_BUCKET}")
                print(f"Error message: {result.stderr}")

print("Backup process completed")
print(f"Results written to {OUTPUT_FILE}")