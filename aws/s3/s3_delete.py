import boto3
import csv
from datetime import datetime, timezone
from botocore.exceptions import ClientError
import concurrent.futures

def delete_bucket_contents(s3_client, bucket_name):
    try:
        # Delete all object versions and delete markers
        versioning = s3_client.get_bucket_versioning(Bucket=bucket_name)
        versioning_enabled = 'Status' in versioning and versioning['Status'] == 'Enabled'

        paginator = s3_client.get_paginator('list_object_versions')
        for page in paginator.paginate(Bucket=bucket_name):
            objects_to_delete = []
            for version in page.get('Versions', []):
                objects_to_delete.append({'Key': version['Key'], 'VersionId': version['VersionId']})
            for marker in page.get('DeleteMarkers', []):
                objects_to_delete.append({'Key': marker['Key'], 'VersionId': marker['VersionId']})
            
            if objects_to_delete:
                s3_client.delete_objects(Bucket=bucket_name, Delete={'Objects': objects_to_delete})

        # Delete remaining objects (if versioning was not enabled)
        if not versioning_enabled:
            paginator = s3_client.get_paginator('list_objects_v2')
            for page in paginator.paginate(Bucket=bucket_name):
                objects_to_delete = [{'Key': obj['Key']} for obj in page.get('Contents', [])]
                if objects_to_delete:
                    s3_client.delete_objects(Bucket=bucket_name, Delete={'Objects': objects_to_delete})

        return True, ""
    except Exception as e:
        return False, str(e)

def delete_bucket(s3_client, bucket_name):
    try:
        # First, delete all contents of the bucket
        contents_deleted, error = delete_bucket_contents(s3_client, bucket_name)
        if not contents_deleted:
            return False, f"Failed to delete bucket contents: {error}"

        # Now attempt to delete the empty bucket
        s3_client.delete_bucket(Bucket=bucket_name)
        return True, ""
    except ClientError as e:
        if e.response['Error']['Code'] == 'AccessDenied':
            return False, f"Access Denied: {str(e)}"
        else:
            return False, str(e)
    except Exception as e:
        return False, str(e)

def process_bucket(s3_client, bucket_name):
    print(f"Processing bucket: {bucket_name}")
    
    success, message = delete_bucket(s3_client, bucket_name)
    status = "YES" if success else "NO"
    if success:
        message = "Deleted successfully"
    
    return {
        'bucket': bucket_name,
        'success': status,
        'message': message
    }

def main():
    aws_access_key_id = input("Enter AWS Access Key ID: ")
    aws_secret_access_key = input("Enter AWS Secret Access Key: ")
    print("Connecting to AWS...")
    
    session = boto3.Session(
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key
    )
    s3_client = session.client('s3')
    
    print("Connected successfully to AWS.")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"./s3-buckets-deleted-{aws_access_key_id}-{timestamp}.csv"
    
    input_file = "buckets_to_delete.txt"
    try:
        with open(input_file, 'r') as file:
            buckets = [line.strip() for line in file if line.strip()]
    except FileNotFoundError:
        print(f"Error: Input file {input_file} not found")
        return

    print(f"Found {len(buckets)} buckets to process.")
    print("WARNING: This script will forcibly delete all listed buckets and their contents without confirmation.")
    print("This action is irreversible and will result in permanent data loss.")
    confirm = input("Type 'DELETE' to confirm and proceed: ")
    if confirm != "DELETE":
        print("Operation cancelled.")
        return
    
    fieldnames = ['bucket', 'success', 'message']
    
    with open(filename, 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            future_to_bucket = {executor.submit(process_bucket, s3_client, bucket): bucket for bucket in buckets}
            for future in concurrent.futures.as_completed(future_to_bucket):
                bucket_info = future.result()
                writer.writerow(bucket_info)
                print(f"Bucket: {bucket_info['bucket']}, Success: {bucket_info['success']}, Message: {bucket_info['message']}")
    
    print(f"Finished processing all buckets.")
    print(f"Results saved in file: {filename}")

if __name__ == "__main__":
    main()