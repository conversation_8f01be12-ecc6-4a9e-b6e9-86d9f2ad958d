import boto3
import csv
from datetime import datetime
import sys
import os
from botocore.config import Config

def get_api_gateway_endpoints(apigw_client):
    """Get all API Gateway REST APIs"""
    print("Retrieving API Gateway REST APIs...")

    # Get all REST APIs
    apis = []
    paginator = apigw_client.get_paginator('get_rest_apis')
    for page in paginator.paginate():
        apis.extend(page['items'])

    return apis

def get_api_resources_and_methods(apigw_client, api_id):
    """Get all resources and their methods for an API"""
    print(f"Retrieving resources and methods for API: {api_id}")

    # Get all resources for this API
    resources = []
    paginator = apigw_client.get_paginator('get_resources')
    for page in paginator.paginate(restApiId=api_id):
        resources.extend(page['items'])

    return resources

def get_method_integration(apigw_client, api_id, resource_id, http_method):
    """Get integration details for a method"""
    try:
        integration = apigw_client.get_integration(
            restApiId=api_id,
            resourceId=resource_id,
            httpMethod=http_method
        )
        return integration
    except Exception as e:
        print(f"Error getting integration for method {http_method} on resource {resource_id}: {str(e)}")
        return None

def extract_lambda_function_name(uri):
    """Extract Lambda function name from the integration URI"""
    if not uri or 'lambda' not in uri.lower():
        return 'N/A'

    # URI format: arn:aws:apigateway:region:lambda:path/2015-03-31/functions/arn:aws:lambda:region:account-id:function:function-name/invocations
    try:
        parts = uri.split(':function:')
        if len(parts) > 1:
            # Extract function name (might include version/alias)
            function_name = parts[1].split('/')[0]
            return function_name
        return 'N/A'
    except Exception:
        return 'N/A'

def main(aws_access_key_id, aws_secret_access_key, region_name="eu-west-1"):
    print("Connecting to AWS...")

    session = boto3.Session(
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key,
        region_name=region_name
    )

    apigw_client = session.client('apigateway', config=Config(read_timeout=60, connect_timeout=60))

    print("Connected successfully to AWS.")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(os.path.dirname(__file__), "output")
    filename = os.path.join(output_dir, f"api-gateway-endpoints-{aws_access_key_id}-{timestamp}.csv")

    # Get all APIs
    apis = get_api_gateway_endpoints(apigw_client)
    print(f"Found {len(apis)} API Gateway APIs")

    fieldnames = [
        'api_name', 'stage', 'public_url', 'resource_path',
        'http_method', 'lambda_function', 'integration_type'
    ]

    with open(filename, 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for api in apis:
            api_id = api['id']
            api_name = api['name']
            print(f"Processing API: {api_name} (ID: {api_id})")

            # Get all deployments and stages for this API
            stages = []
            try:
                stage_response = apigw_client.get_stages(restApiId=api_id)
                stages = stage_response.get('item', [])
            except Exception as e:
                print(f"Error getting stages for API {api_id}: {str(e)}")

            # Get all resources and methods for this API
            resources = get_api_resources_and_methods(apigw_client, api_id)

            for stage in stages:
                stage_name = stage['stageName']
                print(f"  Processing stage: {stage_name}")
                # Construct the base URL for this stage
                base_url = f"https://{api_id}.execute-api.{region_name}.amazonaws.com/{stage_name}"

                for resource in resources:
                    resource_id = resource['id']
                    resource_path = resource.get('path', '/')
                    print(f"    Processing resource: {resource_path}")

                    # Get all methods for this resource
                    methods = resource.get('resourceMethods', {})

                    for http_method in methods:
                        # Skip OPTIONS method which is typically for CORS
                        if http_method == 'OPTIONS':
                            continue
                        print(f"      Processing method: {http_method}")
                        # Get the integration details
                        integration = get_method_integration(apigw_client, api_id, resource_id, http_method)

                        if integration:
                            integration_type = integration.get('type', 'N/A')
                            integration_uri = integration.get('uri', '')

                            # Extract Lambda function name if integration is Lambda
                            lambda_function = 'N/A'
                            if integration_type == 'AWS' or integration_type == 'AWS_PROXY':
                                lambda_function = extract_lambda_function_name(integration_uri)

                            # Write endpoint information to CSV
                            writer.writerow({
                                'api_name': api_name,
                                'stage': stage_name,
                                'public_url': f"{base_url}{resource_path}",
                                'resource_path': resource_path,
                                'http_method': http_method,
                                'lambda_function': lambda_function,
                                'integration_type': integration_type
                            })

                            #print(f"Processed: {api_name} - {http_method} {resource_path}") #Removed duplicate print

    print(f"Finished processing all API Gateway endpoints.")
    print(f"Results saved in file: {filename}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python api_gateway.py <access_key_id> <secret_access_key>")
        sys.exit(1)

    access_key_id = sys.argv[1]
    secret_access_key = sys.argv[2]
    main(access_key_id, secret_access_key)
