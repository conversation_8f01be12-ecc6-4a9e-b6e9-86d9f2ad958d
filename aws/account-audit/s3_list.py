import boto3
import csv
from datetime import datetime, timezone
from botocore.exceptions import ClientError
import concurrent.futures
import sys
import os
from botocore.config import Config

def get_bucket_last_activity(s3_client, bucket_name):
    try:
        print(f"Getting last activity for bucket: {bucket_name}")
        response = s3_client.get_bucket_logging(Bucket=bucket_name)
        if 'LoggingEnabled' in response:
            return response['LoggingEnabled'].get('TargetPrefix', 'No logging data')
        return 'Logging not enabled'
    except Exception as e:
        return f"Error: {str(e)}"

def get_bucket_size_and_last_modified(s3_client, bucket_name):
    try:
        print(f"Getting size and last modified for bucket: {bucket_name}")
        paginator = s3_client.get_paginator('list_objects_v2')
        total_size = 0
        last_modified = None
        for page in paginator.paginate(Bucket=bucket_name):
            for obj in page.get('Contents', []):
                total_size += obj['Size']
                if last_modified is None or obj['LastModified'] > last_modified:
                    last_modified = obj['LastModified']
        return f"{total_size / (1024 * 1024):.2f} MB", last_modified
    except Exception as e:
        return f"Error: {str(e)}", None

def get_bucket_versioning_info(s3_client, bucket_name):
    try:
        print(f"Getting versioning info for bucket: {bucket_name}")
        versioning = s3_client.get_bucket_versioning(Bucket=bucket_name)
        status = versioning.get('Status', 'Not enabled')
        if status == 'Enabled':
            versions = s3_client.list_object_versions(Bucket=bucket_name, MaxKeys=1)
            if 'Versions' in versions and versions['Versions']:
                last_version_date = versions['Versions'][0]['LastModified']
                return status, last_version_date
        return status, None
    except Exception as e:
        return f"Error: {str(e)}", None

def get_account_email(sts_client):
    try:
        response = sts_client.get_caller_identity()
        account_id = response['Account']
        iam_client = boto3.client('iam')
        account_aliases = iam_client.list_account_aliases()['AccountAliases']
        if account_aliases:
            return f"{account_aliases[0]}@{account_id}.aws"
        else:
            return f"root@{account_id}.aws"
    except Exception as e:
        return f"Error retrieving account email: {str(e)}"

def get_bucket_policy(s3_client, bucket_name):
    try:
        print(f"Getting bucket policy for bucket: {bucket_name}")
        policy = s3_client.get_bucket_policy(Bucket=bucket_name)
        return "Policy attached"
    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchBucketPolicy':
            return "No policy"
        else:
            return f"Error: {str(e)}"

def get_bucket_encryption(s3_client, bucket_name):
    try:
        print(f"Getting bucket encryption for bucket: {bucket_name}")
        encryption = s3_client.get_bucket_encryption(Bucket=bucket_name)
        return "Enabled"
    except ClientError as e:
        if e.response['Error']['Code'] == 'ServerSideEncryptionConfigurationNotFoundError':
            return "Not enabled"
        else:
            return f"Error: {str(e)}"

def process_bucket(s3_client, bucket):
    bucket_name = bucket['Name']
    print(f"Processing bucket: {bucket_name}")

    # Get bucket location
    location = s3_client.get_bucket_location(Bucket=bucket_name)['LocationConstraint']
    region = location if location else 'us-east-1'

    # Get bucket versioning status and last version date
    versioning_status, last_version_date = get_bucket_versioning_info(s3_client, bucket_name)

    # Get last activity, size, and last modified date
    last_activity = get_bucket_last_activity(s3_client, bucket_name)
    size, last_modified = get_bucket_size_and_last_modified(s3_client, bucket_name)

    # Get bucket policy and encryption status
    policy_status = get_bucket_policy(s3_client, bucket_name)
    encryption_status = get_bucket_encryption(s3_client, bucket_name)

    return {
        'arn': f"arn:aws:s3:::{bucket_name}",
        'name': bucket_name,
        'creation_date': bucket['CreationDate'].replace(tzinfo=timezone.utc).isoformat(),
        'region': region,
        'last_activity': last_activity,
        'size': size,
        'last_modified': last_modified.replace(tzinfo=timezone.utc).isoformat() if last_modified else 'N/A',
        'versioning': versioning_status,
        'last_version_date': last_version_date.replace(tzinfo=timezone.utc).isoformat() if last_version_date else 'N/A',
        'policy': policy_status,
        'encryption': encryption_status
    }

def main(aws_access_key_id=None, aws_secret_access_key=None):
    if aws_access_key_id is None:
        aws_access_key_id = input("Enter AWS Access Key ID: ")
    if aws_secret_access_key is None:
        aws_secret_access_key = input("Enter AWS Secret Access Key: ")

    print("Connecting to AWS...")

    session = boto3.Session(
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key
    )
    s3_client = session.client('s3', config=Config(read_timeout=60, connect_timeout=60))
    sts_client = session.client('sts', config=Config(read_timeout=60, connect_timeout=60))

    print("Connected successfully to AWS.")
    account_email = get_account_email(sts_client)
    print(f"Account root email: {account_email}")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(os.path.dirname(__file__), "output")
    filename = os.path.join(output_dir, f"s3-buckets-{aws_access_key_id}-{timestamp}.csv")

    print(f"Retrieving S3 bucket information...")
    response = s3_client.list_buckets()
    buckets = response['Buckets']
    bucket_count = len(buckets)
    print(f"Found {bucket_count} buckets.")

    fieldnames = ['arn', 'name', 'creation_date', 'region', 'last_activity', 'size', 'last_modified', 'versioning', 'last_version_date', 'policy', 'encryption', 'account_root_email']

    with open(filename, 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            future_to_bucket = {executor.submit(process_bucket, s3_client, bucket): bucket for bucket in buckets}
            for future in concurrent.futures.as_completed(future_to_bucket):
                bucket_info = future.result()
                if bucket_info:
                    bucket_info['account_root_email'] = account_email
                    writer.writerow(bucket_info)
                bucket = future_to_bucket[future]
                print(f"Finished processing: {bucket.get('Name', 'Unknown')}")

    print(f"Finished processing all buckets.")
    print(f"Results saved in file: {filename}")

if __name__ == "__main__":
    if len(sys.argv) == 3:
        access_key_id = sys.argv[1]
        secret_access_key = sys.argv[2]
        main(access_key_id, secret_access_key)
    else:
        main()
