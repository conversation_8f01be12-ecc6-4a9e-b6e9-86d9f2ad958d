import boto3
import csv
from datetime import datetime
import concurrent.futures
import sys
import os

def get_distribution_details(cloudfront_client, distribution_id):
    """Get detailed information about a specific CloudFront distribution"""
    try:
        print(f"Getting details for distribution: {distribution_id}")
        response = cloudfront_client.get_distribution(Id=distribution_id)
        return response['Distribution']
    except Exception as e:
        print(f"Error getting details for distribution {distribution_id}: {str(e)}")
        return None

def process_distribution(cloudfront_client, distribution_summary):
    """Process a distribution and extract relevant information"""
    distribution_id = distribution_summary['Id']
    print(f"Processing distribution: {distribution_id}")

    # Get full distribution details
    distribution_detail = get_distribution_details(cloudfront_client, distribution_id)

    if not distribution_detail:
        return None

    # Extract distribution configuration
    config = distribution_detail['DistributionConfig']

    # Get origins information
    origins = []
    for origin in config.get('Origins', {}).get('Items', []):
        origins.append(origin.get('DomainName', 'N/A'))

    # Get alternative domain names
    aliases = config.get('Aliases', {}).get('Items', [])

    # Determine status as enabled or disabled
    status = "enabled" if config.get('Enabled', False) else "disabled"

    return {
        'id': distribution_id,
        'description': config.get('Comment', 'N/A'),
        'domain': distribution_summary.get('DomainName', 'N/A'),
        'alternative_domains': ', '.join(aliases) if aliases else 'N/A',
        'origins': ', '.join(origins) if origins else 'N/A',
        'last_modified': distribution_summary.get('LastModifiedTime', 'N/A').strftime('%Y-%m-%d %H:%M:%S')
            if isinstance(distribution_summary.get('LastModifiedTime'), datetime) else 'N/A',
        'status': status
    }

def main(aws_access_key_id=None, aws_secret_access_key=None):
    if aws_access_key_id is None:
        aws_access_key_id = input("Enter AWS Access Key ID: ")
    if aws_secret_access_key is None:
        aws_secret_access_key = input("Enter AWS Secret Access Key: ")

    print("Connecting to AWS...")

    session = boto3.Session(
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key
    )
    cloudfront_client = session.client('cloudfront')

    print("Connected successfully to AWS.")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(os.path.dirname(__file__), "output")
    filename = os.path.join(output_dir, f"cloudfront-distributions-{aws_access_key_id}-{timestamp}.csv")

    print("Retrieving CloudFront distributions...")

    # Get all distributions
    distributions = []
    paginator = cloudfront_client.get_paginator('list_distributions')
    for page in paginator.paginate():
        if 'Items' in page.get('DistributionList', {}):
            distributions.extend(page['DistributionList']['Items'])

    distribution_count = len(distributions)
    print(f"Found {distribution_count} CloudFront distributions.")

    fieldnames = ['id', 'description', 'domain', 'alternative_domains', 'origins',
                 'last_modified', 'status']

    with open(filename, 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            future_to_distribution = {executor.submit(process_distribution, cloudfront_client, dist): dist
                                    for dist in distributions}

            for future in concurrent.futures.as_completed(future_to_distribution):
                distribution_info = future.result()
                if distribution_info:
                    writer.writerow(distribution_info)
                dist = future_to_distribution[future]
                print(f"Finished processing: {dist.get('Id', 'Unknown')}")

    print(f"Finished processing all CloudFront distributions.")
    print(f"Results saved in file: {filename}")

if __name__ == "__main__":
    if len(sys.argv) == 3:
        access_key_id = sys.argv[1]
        secret_access_key = sys.argv[2]
        main(access_key_id, secret_access_key)
    else:
        main()
