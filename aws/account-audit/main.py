import os
import sys
import subprocess
import concurrent.futures
import datetime
import json

def ensure_output_dir():
    output_dir = os.path.join(os.path.dirname(__file__), "output")
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    return output_dir

def run_script(script_name, access_key_id, secret_access_key):
    try:
        print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Running {script_name}...")
        process = subprocess.run(
            [sys.executable, script_name, access_key_id, secret_access_key],
            capture_output=True,
            text=True,
            check=False,  # Don't raise an exception for non-zero exit codes
            env=os.environ,
        )
        print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Finished {script_name}.")
        print(f"Output from {script_name}:")
        print(process.stdout)
        if process.stderr:
            print(f"Error from {script_name}:")
            print(process.stderr)

        return process.stdout, process.stderr

    except subprocess.TimeoutExpired:
        error_message = f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Timeout expired for {script_name}."
        print(error_message)
        return "", error_message
    except FileNotFoundError:
        error_message = f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Script not found: {script_name}"
        print(error_message)
        return "", error_message
    except Exception as e:
        error_message = f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - An unexpected error occurred with {script_name}: {e}"
        print(error_message)
        return "", error_message

if __name__ == "__main__":
    futures = {}  # Initialize futures here
    try:
        with open("credentials.json", "r") as f:
            credentials_list = json.load(f)

        for credentials in credentials_list:
            access_key_id = credentials["access_key_id"]
            secret_access_key = credentials["secret_access_key"]

            output_dir = ensure_output_dir()
            scripts = [
                "api_gateway.py",
                "s3_list.py",
                "cdn_list.py",
                "lambda_list.py",
                "iam_users.py",
            ]

            print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Starting AWS Account Audit")
            with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
                futures = {executor.submit(run_script, os.path.join(os.path.dirname(__file__), script), access_key_id, secret_access_key): script for script in scripts}

                for future in concurrent.futures.as_completed(futures, timeout=300):
                    script = futures[future]
                    if not future.done():
                        print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Script {script} may still be running (check before result).")
                    try:
                        stdout, stderr = future.result()
                    except Exception as e:
                        print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Exception while processing {script}: {e}")

            print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - AWS Account Audit Completed")

    except FileNotFoundError:
        access_key_id = input("Enter your AWS Access Key ID: ")
        secret_access_key = input("Enter your AWS Secret Access Key: ")

        output_dir = ensure_output_dir()

        scripts = [
            "api_gateway.py",
            "s3_list.py",
            "cdn_list.py",
            "lambda_list.py",
            "iam_users.py",
        ]

        print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Starting AWS Account Audit")
        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            futures = {executor.submit(run_script, os.path.join(os.path.dirname(__file__), script), access_key_id, secret_access_key): script for script in scripts}

            for future in concurrent.futures.as_completed(futures, timeout=300):
                script = futures[future]
                if not future.done():
                    print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Script {script} may still be running (check before result).")
                try:
                    stdout, stderr = future.result()
                except Exception as e:
                    print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Exception while processing {script}: {e}")

        print(f"{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - AWS Account Audit Completed")

    except Exception as e:
        print(f"Error reading credentials: {e}")
