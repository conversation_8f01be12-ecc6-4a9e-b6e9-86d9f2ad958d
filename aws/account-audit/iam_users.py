import boto3
import csv
from datetime import datetime, timezone
from botocore.exceptions import ClientError
import concurrent.futures
import sys
import os
from botocore.config import Config

def get_user_policies(iam_client, username):
    try:
        # Get inline policies
        inline_policies = iam_client.list_user_policies(UserName=username)['PolicyNames']
        
        # Get attached policies
        attached_policies = [policy['PolicyName'] for policy in 
                           iam_client.list_attached_user_policies(UserName=username)['AttachedPolicies']]
        
        # Combine both types of policies
        all_policies = inline_policies + attached_policies
        return ', '.join(all_policies) if all_policies else 'No policies'
    except Exception as e:
        return f"Error: {str(e)}"

def get_access_key_last_used(iam_client, access_key_id):
    try:
        response = iam_client.get_access_key_last_used(AccessKeyId=access_key_id)
        last_used = response['AccessKeyLastUsed'].get('LastUsedDate')
        return last_used.replace(tzinfo=timezone.utc).isoformat() if last_used else 'Never used'
    except Exception as e:
        return f"Error: {str(e)}"

def process_user(iam_client, user):
    username = user['UserName']
    print(f"Processing user: {username}")
    
    # Get user policies
    policies = get_user_policies(iam_client, username)
    
    # Get access keys
    try:
        access_keys = iam_client.list_access_keys(UserName=username)['AccessKeyMetadata']
        
        # Process each access key
        user_records = []
        if access_keys:
            for key in access_keys:
                access_key_id = key['AccessKeyId']
                last_used = get_access_key_last_used(iam_client, access_key_id)
                
                user_records.append({
                    'username': username,
                    'user_id': user['UserId'],
                    'user_arn': user['Arn'],
                    'creation_date': user['CreateDate'].replace(tzinfo=timezone.utc).isoformat(),
                    'access_key_id': access_key_id,
                    'access_key_created': key['CreateDate'].replace(tzinfo=timezone.utc).isoformat(),
                    'access_key_status': key['Status'],
                    'last_access_time': last_used,
                    'policies': policies
                })
        else:
            # User without access keys
            user_records.append({
                'username': username,
                'user_id': user['UserId'],
                'user_arn': user['Arn'],
                'creation_date': user['CreateDate'].replace(tzinfo=timezone.utc).isoformat(),
                'access_key_id': 'No access key',
                'access_key_created': 'N/A',
                'access_key_status': 'N/A',
                'last_access_time': 'N/A',
                'policies': policies
            })
        
        return user_records
    except Exception as e:
        print(f"Error processing user {username}: {str(e)}")
        return []

def main(aws_access_key_id=None, aws_secret_access_key=None):
    if aws_access_key_id is None:
        aws_access_key_id = input("Enter AWS Access Key ID: ")
    if aws_secret_access_key is None:
        aws_secret_access_key = input("Enter AWS Secret Access Key: ")

    print("Connecting to AWS...")

    session = boto3.Session(
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key
    )
    iam_client = session.client('iam', config=Config(read_timeout=60, connect_timeout=60))
    
    print("Connected successfully to AWS.")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(os.path.dirname(__file__), "output")
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    filename = os.path.join(output_dir, f"iam-users-{aws_access_key_id}-{timestamp}.csv")
    
    print(f"Retrieving IAM user information...")
    
    # Get all users
    users = []
    paginator = iam_client.get_paginator('list_users')
    for page in paginator.paginate():
        users.extend(page['Users'])
    
    user_count = len(users)
    print(f"Found {user_count} IAM users.")
    
    fieldnames = ['username', 'user_id', 'user_arn', 'creation_date', 'access_key_id', 
                 'access_key_created', 'access_key_status', 'last_access_time', 'policies']
    
    with open(filename, 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            future_to_user = {executor.submit(process_user, iam_client, user): user for user in users}
            for future in concurrent.futures.as_completed(future_to_user):
                user_records = future.result()
                for record in user_records:
                    writer.writerow(record)
    
    print(f"Finished processing all users.")
    print(f"Results saved in file: {filename}")

if __name__ == "__main__":
    if len(sys.argv) == 3:
        access_key_id = sys.argv[1]
        secret_access_key = sys.argv[2]
        main(access_key_id, secret_access_key)
    else:
        main()
