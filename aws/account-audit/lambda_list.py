import boto3
import csv
import concurrent.futures
from datetime import datetime
import sys
import os
from botocore.config import Config

def get_lambda_details(lambda_client, function_name):
    """Get detailed information about a specific Lambda function"""
    try:
        print(f"Getting details for function: {function_name}")
        # Get basic function configuration
        response = lambda_client.get_function(FunctionName=function_name)
        function_details = response['Configuration']

        # Add code location information
        if 'Code' in response:
            function_details['CodeLocation'] = response['Code'].get('Location', 'N/A')
            function_details['RepositoryType'] = response['Code'].get('RepositoryType', 'N/A')
        print(f"Got details for function: {function_name}")
        return function_details
    except Exception as e:
        print(f"Error getting details for function {function_name}: {str(e)}")
        return None

def get_last_log_date(logs_client, function_name):
    """Get the timestamp of the most recent log entry for a Lambda function"""
    try:
        print(f"Getting last log date for function: {function_name}")
        # Construct the log group name
        log_group_name = f"/aws/lambda/{function_name}"

        # Check if log group exists
        print(f"Describing log groups for: {log_group_name}")
        log_groups = logs_client.describe_log_groups(logGroupNamePrefix=log_group_name)
        if not log_groups.get('logGroups'):
            print(f"No log groups found for: {function_name}")
            return "No logs found"

        # Get log streams ordered by last event time
        print(f"Describing log streams for: {log_group_name}")
        response = logs_client.describe_log_streams(
            logGroupName=log_group_name,
            orderBy='LastEventTime',
            descending=True,
            limit=1
        )

        if not response.get('logStreams'):
            print(f"No log streams found for: {function_name}")
            return "No logs found"

        # Get the timestamp from the most recent stream
        latest_stream = response['logStreams'][0]
        if 'lastEventTimestamp' in latest_stream:
            # Convert milliseconds since epoch to datetime
            log_time = datetime.fromtimestamp(latest_stream['lastEventTimestamp'] / 1000)
            print(f"Last log date for {function_name}: {log_time.strftime('%Y-%m-%d %H:%M:%S')}")
            return log_time.strftime('%Y-%m-%d %H:%M:%S')

        print(f"No lastEventTimestamp found for: {function_name}")
        return "No logs found"
    except Exception as e:
        print(f"Error getting logs for function {function_name}: {str(e)}")
        return "Error retrieving logs"

def process_lambda_function(clients, function_summary):
    """Process a Lambda function and extract relevant information"""
    lambda_client, logs_client = clients
    function_name = function_summary['FunctionName']
    function_arn = function_summary['FunctionArn']
    
    print(f"Processing function: {function_name}")
    
    # Get detailed function information
    function_details = get_lambda_details(lambda_client, function_name)
    if not function_details:
        return None

    # Get last log date
    print(f"Before calling get_last_log_date for: {function_name}")
    last_log_date = get_last_log_date(logs_client, function_name)
    print(f"After calling get_last_log_date for: {function_name}")

    # Format timestamps - properly handling LastModified
    if 'LastModified' in function_details:
        try:
            if isinstance(function_details['LastModified'], str):
                # Parse the ISO 8601 timestamp string
                last_modified_dt = datetime.strptime(
                    function_details['LastModified'].split('+')[0], 
                    '%Y-%m-%dT%H:%M:%S.%f'
                )
                last_modified = last_modified_dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                # Handle if it's a timestamp in milliseconds
                last_modified = datetime.fromtimestamp(function_details['LastModified'] / 1000).strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            print(f"Error parsing LastModified for {function_name}: {e}")
            last_modified = str(function_details['LastModified'])
    else:
        last_modified = "Unknown"
    
    return {
        'name': function_name,
        'id': function_arn,
        'description': function_details.get('Description', 'N/A'),
        'runtime': function_details.get('Runtime', 'N/A'),
        'last_modified': last_modified,
        'last_log_date': last_log_date,
        'code_size': f"{function_details.get('CodeSize', 0) / (1024 * 1024):.2f} MB" if function_details.get('CodeSize') else 'N/A'
    }

def main(aws_access_key_id=None, aws_secret_access_key=None, region_name="eu-west-1"):
    if aws_access_key_id is None:
        aws_access_key_id = input("Enter AWS Access Key ID: ")
    if aws_secret_access_key is None:
        aws_secret_access_key = input("Enter AWS Secret Access Key: ")

    print("Connecting to AWS...")

    session = boto3.Session(
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key,
        region_name=region_name
    )

    lambda_client = session.client('lambda', config=Config(read_timeout=60, connect_timeout=60))
    logs_client = session.client('logs', config=Config(read_timeout=60, connect_timeout=60))
    
    print("Connected successfully to AWS.")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(os.path.dirname(__file__), "output")
    filename = os.path.join(output_dir, f"lambda-functions-report-{aws_access_key_id}-{timestamp}.csv")
    
    print("Retrieving Lambda functions...")
    
    # Get all Lambda functions
    functions = []
    paginator = lambda_client.get_paginator('list_functions')
    for page in paginator.paginate():
        functions.extend(page['Functions'])
    
    function_count = len(functions)
    print(f"Found {function_count} Lambda functions.")
    
    fieldnames = [
        'name', 'id', 'description', 'runtime', 
        'last_modified', 'last_log_date', 'code_size'
    ]
    
    with open(filename, 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        clients = (lambda_client, logs_client)
        processed_count = 0
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            future_to_function = {}
            for func in functions:
                print(f"Submitting function to executor: {func['FunctionName']}")
                future = executor.submit(process_lambda_function, clients, func)
                future_to_function[future] = func

            for future in concurrent.futures.as_completed(future_to_function):
                function_info = future.result()
                if function_info:
                    writer.writerow(function_info)
                    processed_count += 1
                    print(f"Progress: {processed_count}/{function_count} functions processed")
                function = future_to_function[future]
                print(f"Finished processing: {function.get('FunctionName', 'Unknown')}")

    print(f"Finished processing all Lambda functions.")
    print(f"Results saved in file: {filename}")

if __name__ == "__main__":
    if len(sys.argv) >= 3:
        access_key_id = sys.argv[1]
        secret_access_key = sys.argv[2]
        region_name = sys.argv[3] if len(sys.argv) == 4 else "eu-west-1"
        main(access_key_id, secret_access_key, region_name)
    else:
        main()
