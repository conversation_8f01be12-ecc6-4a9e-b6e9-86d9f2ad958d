# Repository Archiver

A tool to archive GitHub repositories from one organization to another.

## Process Flow

1. Clone repositories from source org
2. Create repositories in target org (only for successful clones)
3. Push all content to target org (only for successful creations)
4. Optionally delete source repositories (only for successful pushes)
5. Generate summary report of all operations

## Features

- Clones repositories from source organization
- Creates new repositories in target organization
- Pushes all content to target repositories
- Optionally deletes source repositories after successful archival
- Interactive prompts for configuration
- Detailed progress and error reporting

## Prerequisites

- [Bun](https://bun.sh/) runtime (v1.0.0 or higher)
- Git installed and configured
- GitHub account with appropriate permissions
- GitHub Personal Access Token (PAT)

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd archive-repo

# Install dependencies
bun install
```

## GitHub Personal Access Token Setup

To use this tool, you need a GitHub Personal Access Token with the following minimum permissions:

1. Go to GitHub Settings → Developer Settings → Personal Access Tokens → Tokens (classic)
2. Click "Generate new token (classic)"
3. Give it a descriptive name (e.g., "Repository Archiver")
4. Set an expiration date
5. Select the following scopes:
   - `repo` (Full control of private repositories)
     - Required for cloning, creating, and deleting repositories
   - `delete_repo` (Delete repositories)
     - Required only if you plan to delete source repositories after archival

6. Click "Generate token"
7. **IMPORTANT**: Copy the token immediately and store it securely. You won't be able to see it again.

## Usage

1. Create a text file named `repo_names_to_archive.txt` in the project directory
2. Add one repository name per line (without the organization name)
3. Run the script:

```bash
bun start
```

4. Follow the interactive prompts to provide:
   - Source organization name (default: "Together-Digital")
   - Target organization name (default: "together-digital-archive")
   - GitHub Personal Access Token
   - Working directory path (default: `./gh_repos_backup`)
   - Repository list file path (default: `./repo_names_to_archive.txt`)

5. Confirm to proceed with the archival process

## Example `repo_names_to_archive.txt`

```
repo1
repo2
repo3
```

## Security Notes

- The GitHub PAT is only used locally and is not stored anywhere
- The token is only used for the duration of the script execution
- Use the minimum required permissions when generating the PAT
- Consider using a short expiration date for the PAT

