import { exec } from "child_process";
import { promises as fs } from "fs";
import { promisify } from "util";
import { Octokit } from "@octokit/rest";
import path from "path";
import inquirer from "inquirer";

const execAsync = promisify(exec);

interface Config {
	sourceOrg: string;
	targetOrg: string;
	workingDir: string;
	repoListPath: string;
	githubToken: string;
}

interface RepoStatus {
	name: string;
	cloned: boolean;
	created: boolean;
	pushed: boolean;
	deleted: boolean;
	error?: string;
}

const defaultConfig: Config = {
	sourceOrg: "Together-Digital",
	targetOrg: "together-digital-archive",
	workingDir: path.join(process.cwd(), "gh_repos_backup"),
	repoListPath: path.join(process.cwd(), "repo_names_to_archive.txt"),
	githubToken: "",
};

export class RepoArchiver {
	private config: Config;
	private octokit!: Octokit;
	private repoStatuses: Map<string, RepoStatus>;
	private timestamp: string;

	constructor(config: Partial<Config> = {}) {
		this.config = { ...defaultConfig, ...config };
		this.repoStatuses = new Map();
		this.timestamp = new Date().toISOString().replace(/[:.]/g, "-");
	}

	private async promptForConfig(): Promise<void> {
		const answers = await inquirer.prompt([
			{
				type: "input",
				name: "sourceOrg",
				message: "Source organization name:",
				default: this.config.sourceOrg,
			},
			{
				type: "input",
				name: "targetOrg",
				message: "Target organization name:",
				default: this.config.targetOrg,
			},
			{
				type: "input",
				name: "githubToken",
				message: "GitHub Personal Access Token:",
				default: this.config.githubToken,
			},
			{
				type: "input",
				name: "workingDir",
				message: "Working directory path:",
				default: this.config.workingDir,
			},
			{
				type: "input",
				name: "repoListPath",
				message: "Repository list file path:",
				default: this.config.repoListPath,
			},
		]);

		this.config = { ...this.config, ...answers };
		this.octokit = new Octokit({ auth: this.config.githubToken });
	}

	private async validateConfig() {
		if (!this.config.sourceOrg || !this.config.targetOrg || !this.config.githubToken) {
			throw new Error("Missing required configuration. Please provide all required values.");
		}
	}

	private async readRepoList(): Promise<string[]> {
		const content = await fs.readFile(this.config.repoListPath, "utf-8");
		return content.split("\n").filter(Boolean);
	}

	private async validateRepoExists(org: string, repo: string): Promise<boolean> {
		try {
			await this.octokit.repos.get({ owner: org, repo });
			return true;
		} catch (error) {
			if (error instanceof Error && "status" in error && error.status === 404) {
				return false;
			}
			throw error;
		}
	}

	private async cloneRepos(repos: string[]) {
		const timestampedDir = path.join(this.config.workingDir, this.timestamp);
		await fs.mkdir(timestampedDir, { recursive: true });

		for (const repo of repos) {
			try {
				const repoUrl = `https://github.com/${this.config.sourceOrg}/${repo}.git`;
				const repoPath = path.join(timestampedDir, `${repo}.git`);

				console.log(`Cloning ${repo}...`);
				await execAsync(`git clone --mirror ${repoUrl} ${repoPath}`);

				const exists = await fs
					.access(repoPath)
					.then(() => true)
					.catch(() => false);
				if (!exists) {
					throw new Error("Repository clone failed - directory not created");
				}

				this.repoStatuses.set(repo, {
					name: repo,
					cloned: true,
					created: false,
					pushed: false,
					deleted: false,
				});
				console.log(`Successfully cloned ${repo}`);
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : "Unknown error";
				this.repoStatuses.set(repo, {
					name: repo,
					cloned: false,
					created: false,
					pushed: false,
					deleted: false,
					error: `Clone failed: ${errorMessage}`,
				});
				console.error(`Failed to clone ${repo}:`, errorMessage);
			}
		}
	}

	private async createTargetRepos(repos: string[]) {
		for (const repo of repos) {
			const status = this.repoStatuses.get(repo);
			if (!status?.cloned) {
				console.log(`Skipping ${repo} creation - clone was not successful`);
				continue;
			}

			try {
				const exists = await this.validateRepoExists(
					this.config.targetOrg,
					repo
				);
				if (exists) {
					console.log(`Repo ${repo} already exists in target org`);
					status.created = true;
					continue;
				}

				await this.octokit.repos.createInOrg({
					org: this.config.targetOrg,
					name: repo,
					private: true,
					has_issues: false,
					has_projects: false,
					has_wiki: false,
				});

				status.created = true;
				console.log(`Created target repo: ${repo}`);
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : "Unknown error";
				status.error = `Creation failed: ${errorMessage}`;
				console.error(`Failed to create ${repo}:`, errorMessage);
			}
		}
	}

	private async validatePush(repo: string): Promise<boolean> {
		try {
			const repoPath = path.join(this.config.workingDir, this.timestamp, `${repo}.git`);
			const { stdout } = await execAsync(
				`cd ${repoPath} && git ls-remote target`
			);
			return stdout.trim().length > 0;
		} catch {
			return false;
		}
	}

	private async pushToTarget(repos: string[]) {
		for (const repo of repos) {
			const status = this.repoStatuses.get(repo);
			if (!status?.created) {
				console.log(`Skipping ${repo} push - creation was not successful`);
				continue;
			}

			try {
				const repoPath = path.join(this.config.workingDir, this.timestamp, `${repo}.git`);
				const targetUrl = `**************:${this.config.targetOrg}/${repo}.git`;

				console.log(`Pushing ${repo} to target...`);
				
				// First, try to remove any existing target remote
				try {
					await execAsync(`cd ${repoPath} && git remote remove target`);
				} catch {
					// Ignore error if remote doesn't exist
				}

				// Add target remote and push all refs except pull requests
				await execAsync(
					`cd ${repoPath} && git remote add target ${targetUrl} && git push target 'refs/heads/*' 'refs/tags/*'`
				);

				const pushSuccessful = await this.validatePush(repo);
				if (!pushSuccessful) {
					throw new Error("Push validation failed");
				}

				status.pushed = true;
				console.log(`Successfully pushed ${repo} to target`);
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : "Unknown error";
				status.error = `Push failed: ${errorMessage}`;
				console.error(`Failed to push ${repo}:`, errorMessage);
			}
		}
	}

	private async deleteSourceRepos(repos: string[]) {
		const failedRepos = repos.filter((repo) => {
			const status = this.repoStatuses.get(repo);
			return !status?.pushed;
		});

		if (failedRepos.length > 0) {
			console.error(
				"The following repositories were not successfully archived and will not be deleted:"
			);
			failedRepos.forEach((repo) => {
				const status = this.repoStatuses.get(repo);
				console.error(`- ${repo}: ${status?.error || "Unknown error"}`);
			});

			const proceed = await this.promptConfirmation(
				"Do you want to proceed with deleting the successfully archived repositories?"
			);
			if (!proceed) {
				console.log("Deletion cancelled");
				return;
			}
		}

		for (const repo of repos) {
			const status = this.repoStatuses.get(repo);
			if (!status?.pushed) {
				console.log(`Skipping ${repo} deletion - push was not successful`);
				continue;
			}

			try {
				await this.octokit.repos.delete({
					owner: this.config.sourceOrg,
					repo,
				});
				status.deleted = true;
				console.log(`Deleted source repo: ${repo}`);
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : "Unknown error";
				status.error = `Deletion failed: ${errorMessage}`;
				console.error(`Failed to delete ${repo}:`, errorMessage);
			}
		}
	}

	private async printSummary() {
		console.log("\nRepository Archive Summary:");
		console.log("---------------------------");

		const successful = Array.from(this.repoStatuses.values()).filter(
			(status) => status.pushed && !status.error
		);
		const failed = Array.from(this.repoStatuses.values()).filter(
			(status) => status.error || !status.pushed
		);

		console.log(`\nSuccessful: ${successful.length}`);
		successful.forEach((status) => {
			console.log(`✓ ${status.name}`);
		});

		console.log(`\nFailed: ${failed.length}`);
		failed.forEach((status) => {
			console.log(`✗ ${status.name}: ${status.error || "Unknown error"}`);
		});
	}

	private async promptConfirmation(message: string): Promise<boolean> {
		const { proceed } = await inquirer.prompt([
			{
				type: "confirm",
				name: "proceed",
				message,
				default: false,
			},
		]);
		return proceed;
	}

	async archive() {
		try {
			await this.promptForConfig();
			await this.validateConfig();

			const repos = await this.readRepoList();
			if (repos.length === 0) {
				console.log("No repositories found to archive");
				return;
			}

			console.log(`Found ${repos.length} repositories to archive`);
			const { proceed } = await inquirer.prompt([
				{
					type: "confirm",
					name: "proceed",
					message: `Do you want to proceed with archiving ${repos.length} repositories?`,
					default: false,
				},
			]);

			if (!proceed) {
				console.log("Operation cancelled");
				return;
			}

			await this.cloneRepos(repos);
			await this.createTargetRepos(repos);
			await this.pushToTarget(repos);
			await this.deleteSourceRepos(repos);
			await this.printSummary();
		} catch (error) {
			console.error("Archive operation failed:", error);
			process.exit(1);
		}
	}
}
