import { writeFileSync } from "fs";

interface VercelProject {
  id: string;
  name: string;
  accountId: string;
  createdAt: number;
  updatedAt: number;
  framework?: string;
  nodeVersion?: string;
  buildCommand?: string;
  devCommand?: string;
  installCommand?: string;
  outputDirectory?: string;
  rootDirectory?: string;
  directoryListing?: boolean;
  autoExposeSystemEnvs?: boolean;
  autoAssignCustomDomains?: boolean;
  prioritizeProductionBuilds?: boolean;
  analytics?: {
    id: string;
    enabledAt?: number;
    disabledAt?: number;
  };
  link?: {
    type: string;
    repo: string;
    repoId: string;
    gitCredentialId: string;
    productionBranch: string;
    deployHooks?: Array<{
      url: string;
      [key: string]: any;
    }>;
    autoDeploy?: boolean;
    autoDeployProduction?: boolean;
  };
  domains?: string[];
  env?: Array<{
    type: string;
    key: string;
    value: string;
    target?: string[];
    gitBranch?: string;
    configurationId?: string;
    updatedAt?: number;
    createdAt?: number;
  }>;
  latestDeployments?: Array<{
    id: string;
    name: string;
    url: string;
    state: string;
    createdAt: number;
    updatedAt: number;
  }>;
  functions?: {
    regions?: string[];
  };
  cronJobs?: Array<{
    name: string;
    schedule: string;
    command: string;
    [key: string]: any;
  }>;
}

interface VercelProjectsResponse {
  projects: VercelProject[];
  pagination: {
    count: number;
    next: number | null;
    prev: number | null;
  };
}

interface VercelTeam {
  id: string;
  name: string;
  slug: string;
}

interface VercelDomain {
  name: string;
  type: string;
  verified: boolean;
  configuration?: {
    misconfigured: boolean;
  };
  gitBranch?: string;
}

interface VercelDomainsResponse {
  domains: VercelDomain[];
}

const VERCEL_API_TOKEN = process.env.VERCEL_API_TOKEN;
if (!VERCEL_API_TOKEN) {
  throw new Error("VERCEL_API_TOKEN environment variable is required");
}

const escapeCSV = (value: any): string => {
  if (value === null || value === undefined) return "";
  const stringValue = String(value);
  if (stringValue.includes(",") || stringValue.includes('"') || stringValue.includes("\n")) {
    return `"${stringValue.replace(/"/g, '""')}"`;
  }
  return stringValue;
};

const formatArray = (arr: any[] | undefined): string => {
  if (!arr?.length) return "";
  return arr.map(escapeCSV).join(";");
};

const formatEnv = (env: VercelProject["env"]): string => {
  if (!env?.length) return "";
  return env.map(e => `${e.key}=${e.value}`).join(";");
};

const formatDeployments = (deployments: VercelProject["latestDeployments"]): string => {
  if (!deployments?.length) return "";
  return deployments.map(d => `${d.name}(${d.state})`).join(";");
};

const formatDeployHooks = (hooks: any[] | undefined): string => {
  if (!hooks?.length) return "";
  return hooks.map(hook => hook.url || "").filter(Boolean).join(";");
};

const formatCronJobs = (jobs: VercelProject["cronJobs"]): string => {
  if (!jobs?.length) return "";
  return jobs.map(job => `${job.name}:${job.schedule}`).join(";");
};

const getTeams = async (): Promise<VercelTeam[]> => {
  console.log("Fetching Vercel teams...");
  const response = await fetch("https://api.vercel.com/v2/teams", {
    headers: {
      Authorization: `Bearer ${VERCEL_API_TOKEN}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch teams: ${response.statusText}`);
  }

  const data = await response.json();
  return data.teams;
};

const getProjects = async (teamId?: string): Promise<VercelProject[]> => {
  console.log(`Fetching Vercel projects${teamId ? ` for team ${teamId}` : ""}...`);
  const allProjects: VercelProject[] = [];
  let nextPage: number | null = 1;

  while (nextPage) {
    const url = new URL("https://api.vercel.com/v9/projects");
    url.searchParams.append("limit", "100");
    if (nextPage !== 1) {
      url.searchParams.append("until", nextPage.toString());
    }
    if (teamId) {
      url.searchParams.append("teamId", teamId);
    }

    const response = await fetch(url.toString(), {
      headers: {
        Authorization: `Bearer ${VERCEL_API_TOKEN}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to fetch projects: ${response.statusText}\n${errorText}`);
    }

    const data = (await response.json()) as VercelProjectsResponse;
    allProjects.push(...data.projects);
    nextPage = data.pagination.next;
    
    if (data.projects.length === 0) {
      console.log("No more projects found");
      break;
    }
  }

  console.log(`Found ${allProjects.length} projects${teamId ? ` for team ${teamId}` : ""}`);
  return allProjects;
};

const getProjectDetails = async (projectId: string, teamId?: string): Promise<VercelProject & { 
  productionCustomDomains: string[], 
  productionVercelDomains: string[], 
  previewCustomDomains: string[], 
  previewVercelDomains: string[],
  projectUrl: string
}> => {
  const url = new URL(`https://api.vercel.com/v9/projects/${projectId}`);
  if (teamId) {
    url.searchParams.append("teamId", teamId);
  }

  const response = await fetch(url.toString(), {
    headers: {
      Authorization: `Bearer ${VERCEL_API_TOKEN}`,
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to fetch project details: ${response.statusText}\n${errorText}`);
  }

  const project = await response.json();
  
  // Fetch domains for the project
  const domainsUrl = new URL(`https://api.vercel.com/v9/projects/${projectId}/domains`);
  if (teamId) {
    domainsUrl.searchParams.append("teamId", teamId);
  }
  
  const domainsResponse = await fetch(domainsUrl.toString(), {
    headers: {
      Authorization: `Bearer ${VERCEL_API_TOKEN}`,
    },
  });
  
  let productionCustomDomains: string[] = [];
  let productionVercelDomains: string[] = [];
  let previewCustomDomains: string[] = [];
  let previewVercelDomains: string[] = [];
  
  if (domainsResponse.ok) {
    const domainsData = await domainsResponse.json() as VercelDomainsResponse;
    
    // Get the production branch from the project
    const productionBranch = project.link?.productionBranch || "main";
    
    // First, collect all domains
    const allDomains: { name: string; isProduction: boolean; isVercelDomain: boolean }[] = [];
    
    domainsData.domains?.forEach(domain => {
      const isProduction = !domain.gitBranch || domain.gitBranch === productionBranch;
      const isVercelDomain = domain.name.includes("vercel.app");
      
      allDomains.push({
        name: domain.name,
        isProduction,
        isVercelDomain
      });
    });
    
    // Create a map of naked domains to their www counterparts
    const nakedToWWW = new Map<string, string>();
    const wwwToNaked = new Map<string, string>();
    
    allDomains.forEach(domain => {
      if (domain.name.startsWith('www.')) {
        const nakedDomain = domain.name.replace('www.', '');
        wwwToNaked.set(domain.name, nakedDomain);
      } else {
        const wwwDomain = `www.${domain.name}`;
        nakedToWWW.set(domain.name, wwwDomain);
      }
    });
    
    // Filter domains, preferring naked domains over www
    allDomains.forEach(domain => {
      // Skip www domains if we have the naked version
      if (domain.name.startsWith('www.') && allDomains.some(d => d.name === wwwToNaked.get(domain.name))) {
        return;
      }
      
      if (domain.isProduction) {
        if (domain.isVercelDomain) {
          productionVercelDomains.push(domain.name);
        } else {
          productionCustomDomains.push(domain.name);
        }
      } else {
        if (domain.isVercelDomain) {
          previewVercelDomains.push(domain.name);
        } else {
          previewCustomDomains.push(domain.name);
        }
      }
    });
  }
  
  // Generate project URL in the correct format
  const teamSlug = teamId ? await getTeamSlug(teamId) : "personal";
  const projectUrl = `https://vercel.com/${teamSlug}/${project.name}`;
  
  // Fetch project settings
  const settingsUrl = new URL(`https://api.vercel.com/v9/projects/${projectId}/settings`);
  if (teamId) {
    settingsUrl.searchParams.append("teamId", teamId);
  }
  
  const settingsResponse = await fetch(settingsUrl.toString(), {
    headers: {
      Authorization: `Bearer ${VERCEL_API_TOKEN}`,
    },
  });
  
  if (settingsResponse.ok) {
    const settingsData = await settingsResponse.json();
    project.prioritizeProductionBuilds = settingsData.prioritizeProductionBuilds;
    project.functions = settingsData.functions;
  }
  
  // Fetch cron jobs for the project
  const cronJobsUrl = new URL(`https://api.vercel.com/v9/projects/${projectId}/cron-jobs`);
  if (teamId) {
    cronJobsUrl.searchParams.append("teamId", teamId);
  }
  
  const cronJobsResponse = await fetch(cronJobsUrl.toString(), {
    headers: {
      Authorization: `Bearer ${VERCEL_API_TOKEN}`,
    },
  });
  
  if (cronJobsResponse.ok) {
    const cronJobsData = await cronJobsResponse.json();
    project.cronJobs = cronJobsData.cronJobs || [];
  }
  
  // Fetch analytics settings
  const analyticsUrl = new URL(`https://api.vercel.com/v9/projects/${projectId}/analytics`);
  if (teamId) {
    analyticsUrl.searchParams.append("teamId", teamId);
  }
  
  const analyticsResponse = await fetch(analyticsUrl.toString(), {
    headers: {
      Authorization: `Bearer ${VERCEL_API_TOKEN}`,
    },
  });
  
  if (analyticsResponse.ok) {
    const analyticsData = await analyticsResponse.json();
    project.analytics = analyticsData;
  }
  
  return {
    ...project,
    productionCustomDomains,
    productionVercelDomains,
    previewCustomDomains,
    previewVercelDomains,
    projectUrl
  };
};

// Helper function to get team slug
const getTeamSlug = async (teamId: string): Promise<string> => {
  try {
    const response = await fetch(`https://api.vercel.com/v2/teams/${teamId}`, {
      headers: {
        Authorization: `Bearer ${VERCEL_API_TOKEN}`,
      },
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.slug;
    }
  } catch (error) {
    console.error(`Failed to fetch team slug for team ${teamId}:`, error);
  }
  
  return "unknown-team";
};

const main = async () => {
  const startTime = Date.now();
  let processedProjects = 0;
  let failedProjects = 0;

  try {
    console.log("Starting Vercel project audit...");
    
    const teams = await getTeams();
    console.log(`Found ${teams.length} teams`);
    
    const csvRows = [
      [
        "Team",
        "Name",
        "ID",
        "Created At",
        "Framework",
        "Node Version",
        "Git Repo",
        "Production Branch",
        "Deploy Hooks",
        "Project URL",
        "Production Custom Domains",
        "Production Vercel Domains",
        "Preview Custom Domains",
        "Preview Vercel Domains",
      ].map(escapeCSV).join(","),
    ];

    for (const team of teams) {
      try {
        const projects = await getProjects(team.id);
        
        for (const project of projects) {
          try {
            console.log(`Processing project: ${project.name} (${project.id}) in team ${team.name}`);
            const details = await getProjectDetails(project.id, team.id);

            const row = [
              team.name,
              details.name,
              details.id,
              new Date(details.createdAt).toISOString(),
              details.framework,
              details.nodeVersion,
              details.link?.repo,
              details.link?.productionBranch,
              formatDeployHooks(details.link?.deployHooks),
              details.projectUrl,
              formatArray(details.productionCustomDomains),
              formatArray(details.productionVercelDomains),
              formatArray(details.previewCustomDomains),
              formatArray(details.previewVercelDomains),
            ]
              .map(escapeCSV)
              .join(",");

            csvRows.push(row);
            processedProjects++;
            console.log(`✓ Completed project: ${project.name}`);
          } catch (error) {
            failedProjects++;
            console.error(`✗ Failed to process project ${project.name} (${project.id}):`, error);
          }
        }
      } catch (error) {
        console.error(`✗ Failed to process team ${team.name}:`, error);
      }
    }

    const outputPath = "vercel-projects-audit.csv";
    writeFileSync(outputPath, csvRows.join("\n"));
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(`
Audit completed!
Duration: ${duration}s
Processed projects: ${processedProjects}
Failed projects: ${failedProjects}
Results saved to: ${outputPath}
`);
  } catch (error) {
    console.error("Fatal error during project audit:", error);
    process.exit(1);
  }
};

main(); 