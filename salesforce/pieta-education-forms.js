// I hate the hardcoded credentials here, but I have received the script like this and I don't have the time to refactor it. Just documenting. Sorry - <PERSON>

const CONSUMER_KEY =
	"3MVG99VEEJ_Bj3.70c42V828jPtla6FBCIWP.tFz1cDhZQ0X4s5mlfpH4sgXVyY4YdDvlgKxFs8bFJbDF7tVi";
const CONSUMER_SECRET =
	"****************************************************************";

// https://pietahouse--dev.sandbox.my.salesforce.com/services/data/v58.0/sobjects/Education_and_Training__c

const getAccessToken = async () => {
	const response = await fetch(
		"https://pietahouse--dev.sandbox.my.salesforce.com/services/oauth2/token",
		{
			method: "POST",
			body: new URLSearchParams({
				grant_type: "client_credentials",
				client_id: CONSUMER_KEY,
				client_secret: CONSUMER_SECRET,
			}),
		}
	);
	return await response.json();
};

import jsforce from "jsforce";
const { access_token, instance_url } = await getAccessToken();
const conn = new jsforce.Connection({
	instanceUrl: instance_url,
	accessToken: access_token,
});

// Get all fields in Education & Training
conn.describe("Education_and_Training__c").then((data) => {
	for (const field of data.fields) {
		console.log(field.name);
	}
});
