import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import fs from 'fs';
import path from 'path';

const SPACE_IDS: <PERSON><PERSON>lyA<PERSON><PERSON><number> = [
  253801,
  252601,
  41541,
  171049,
  79632,
  127568,
  326199,
  311724,
  48920,
  269973,
  90669,
  319175,
  138143,
  173689,
  70720,
  116886,
  96408,
  179859,
  41598,
  46384,
  163700,
  122463,
  120734,
  47927,
  96406,
  277864,
  96206,
  190254,
  188302,
  110648,
  141680,
  153558,
  137444,
  246959,
  124946,
  54430,
  89820,
  264858,
  202329,
  265949,
  50477,
  59991,
  63481,
  58406,
  157613,
  120607,
  69591,
  220868,
  70398,
  190245,
  283551,
  328955,
  328957,
  65451,
  59981,
  138101,
  46358,
  41460,
  94067,
  261605,
  41082,
  41370,
  43933,
  323741,
  168415,
  74720,
  63871,
  85534,
  185871,
  43302,
  67940,
  57339,
  145018,
  71554,
  284390,
  58861,
  61628,
  72943,
  260250,
  272890,
  309290,
  80648,
  73281,
  65906,
  328956,
  311335,
  93633,
  109416,
  324049,
  94623,
  106081,
  152187,
] as const;

const STORYBLOK_BASE_URL = 'https://app.storyblok.com' as const;
const LOGIN_URL = `${STORYBLOK_BASE_URL}/#/login` as const;
const DASHBOARD_URL = `${STORYBLOK_BASE_URL}/#/me/spaces` as const;
const PDF_OUTPUT_DIR = 'pdfs' as const;

// --- Configuration for connecting to an existing Chrome instance ---
// Set to true to connect to an existing Chrome instance, false to launch a new one.
const CONNECT_TO_EXISTING_CHROME = true as const; // <-- MODIFY THIS AS NEEDED
// If CONNECT_TO_EXISTING_CHROME is true, set this to your Chrome's WebSocket endpoint.
// e.g., 'ws://127.0.0.1:9222/devtools/browser/your-unique-id'
// You can find this by launching Chrome with --remote-debugging-port=9222
// and then navigating to http://127.0.0.1:9222/json/version in a browser.
const CHROME_WEBSOCKET_ENDPOINT = 'ws://127.0.0.1:9222/devtools/browser/74dc4eff-8361-4721-b448-3fe999e9d23d' as const; // <-- PUT YOUR WEBSOCKET URL HERE

const VIEWPORT_4K = { width: 3840, height: 2160 } as const;

const sanitizeFilename = (title: string): string => {
  return title.replace(/[\\/:*?"<>|]/g, '-').replace(/\s+/g, '_') + '.pdf';
};

const ensureDirectoryExists = (dirPath: string): void => {
  if (!fs.existsSync(dirPath)) {
    try {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`Created directory: ${dirPath}`);
    } catch (error) {
      console.error(`Error creating directory ${dirPath}:`, error);
      throw error; // Re-throw to stop execution if directory creation fails
    }
  }
};

const main = async (): Promise<void> => {
  let browser: Browser | undefined;
  ensureDirectoryExists(PDF_OUTPUT_DIR);

  try {
    console.log('Configuring browser connection...');
    if (CONNECT_TO_EXISTING_CHROME) {
      if (!CHROME_WEBSOCKET_ENDPOINT) {
        console.error('Error: CHROME_WEBSOCKET_ENDPOINT is not set. Please provide the WebSocket URL.');
        return;
      }
      console.log(`Connecting to existing Chrome instance at: ${CHROME_WEBSOCKET_ENDPOINT}`);
      browser = await puppeteer.connect({ 
        browserWSEndpoint: CHROME_WEBSOCKET_ENDPOINT,
        defaultViewport: VIEWPORT_4K // Set default viewport for connected browser
      });
    } else {
      console.log('Launching new Chrome instance...');
      browser = await puppeteer.launch({ 
          headless: false,
          userDataDir: './user_data', 
          defaultViewport: VIEWPORT_4K, // Set default viewport for launched browser
          args: [
              '--disable-blink-features=AutomationControlled',
              // You might need to adjust --start-maximized or other args for 4K if issues arise
              // '--start-maximized' 
          ]
      });
    }

    console.log('Browser connected/launched successfully.');
    const page: Page = await browser.newPage();
    // Ensure viewport is set, although defaultViewport should handle it.
    await page.setViewport(VIEWPORT_4K);

    await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36');

    console.log(`Navigating to login page: ${LOGIN_URL}`);
    await page.goto(LOGIN_URL, { waitUntil: 'networkidle0' });

    console.log('Please log in to Storyblok. Waiting for navigation to the main dashboard...');
    console.log(`Waiting for URL: ${DASHBOARD_URL}`);
    
    // Wait for user to login and navigate to the main dashboard
    // Increased timeout to 10 minutes to give ample time for login.
    // await page.waitForURL(DASHBOARD_URL, { timeout: 600000 }); 
    await new Promise<void>((resolve, reject) => {
      const checkUrl = async () => {
        if (page.url().startsWith(DASHBOARD_URL)) {
          resolve();
        } else if (Date.now() - startTime > timeout) {
          reject(new Error(`Timeout waiting for URL: ${DASHBOARD_URL}`));
        } else {
          setTimeout(checkUrl, 1000); // Check every second
        }
      };
      const startTime = Date.now();
      const timeout = 600000; // 10 minutes
      checkUrl();
    });
    console.log('Successfully navigated to the main dashboard.');

    for (const spaceId of SPACE_IDS) {
      const spaceDashboardUrl = `${STORYBLOK_BASE_URL}/#/me/spaces/${spaceId}/dashboard`;
      console.log(`Navigating to space dashboard: ${spaceDashboardUrl}`);
      try {
        await page.goto(spaceDashboardUrl, { waitUntil: 'networkidle0', timeout: 60000 });
        
        // Wait a bit for dynamic content to potentially load, e.g., the title
        // await page.waitForTimeout(5000); 
        await new Promise(resolve => setTimeout(resolve, 5000));

        const title = await page.title();
        const filename = sanitizeFilename(title);
        const pdfPath = path.join(PDF_OUTPUT_DIR, filename);

        console.log(`Generating PDF for "${title}" (Space ID: ${spaceId}) -> ${pdfPath}`);
        await page.pdf({ path: pdfPath, format: 'A4', printBackground: true });
        console.log(`Successfully generated PDF: ${pdfPath}`);

      } catch (navigationError) {
        console.error(`Error processing space ID ${spaceId} at ${spaceDashboardUrl}:`, navigationError);
        console.log(`Skipping space ID ${spaceId} due to error.`);
      }
    }

    console.log('All spaces processed.');

  } catch (error) {
    console.error('An error occurred:', error);
  } finally {
    if (browser) {
      console.log('Closing browser connection...');
      if (CONNECT_TO_EXISTING_CHROME) {
        await browser.disconnect();
      } else {
        await browser.close();
      }
    }
  }
};

main().catch(console.error); 