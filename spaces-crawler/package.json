{"name": "storyblok-spaces-crawler", "version": "1.0.0", "description": "A script to crawl Storyblok spaces and generate PDFs.", "main": "dist/index.js", "scripts": {"start": "ts-node src/index.ts", "build": "tsc"}, "keywords": ["storyblok", "puppeteer", "automation", "pdf"], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^20.12.12", "@types/puppeteer": "^7.0.4", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "dependencies": {"puppeteer": "^22.10.0"}}