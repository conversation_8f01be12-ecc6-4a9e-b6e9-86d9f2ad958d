import StoryblokClient from "storyblok-js-client";
import fs from "fs/promises";
import path from "path";

export interface Story {
	id: number;
	name: string;
	// ...other story properties
}

export class StoryblokManager {
	private mapiClient: StoryblokClient;
	private cdnClient: StoryblokClient;
	private spaceId: string;

	constructor(oauthToken: string, previewToken: string, spaceId: string) {
		this.mapiClient = new StoryblokClient({
			oauthToken,
			region: "eu",
		});

		this.cdnClient = new StoryblokClient({
			accessToken: previewToken,
			region: "eu",
		});
		this.spaceId = spaceId;
	}

	async getStoryById(id: number): Promise<Story> {
		const response = await this.cdnClient.get(`cdn/stories/${id}`, {
			version: "draft",
		});
		return response.data.story;
	}

	async backupStories(storyIds: number[]): Promise<Story[]> {
		const stories: Story[] = [];

		for (const id of storyIds) {
			const story = await this.getStoryById(id);
			stories.push(story);
		}

		const date = new Date().toISOString().split('T')[0];
		const backupFileName = `stories-to-delete-backup-${date}.json`;
		const backupPath = path.join(__dirname, "../data", backupFileName);

		await fs.writeFile(backupPath, JSON.stringify(stories, null, 2));
		return stories;
	}

	async deleteStory(id: number): Promise<void> {
		await this.mapiClient.delete(`spaces/${this.spaceId}/stories/${id}`);
	}

	async deleteStories(storyIds: number[]): Promise<void> {
		for (const id of storyIds) {
			await this.deleteStory(id);
			console.log(`Deleted story ${id}`);
		}
	}
}
