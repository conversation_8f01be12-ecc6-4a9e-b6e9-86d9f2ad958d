import prompts from "prompts";
import { readFile } from "fs/promises";
import path from "path";
import { StoryblokManager } from "./storyblok";
import "dotenv/config";

async function main() {
	console.log("\n🗑️  Delete Selected Stories from Storyblok\n");

	const { spaceId } = await prompts({
		type: "text",
		name: "spaceId",
		message: "Enter your Space ID:",
		validate: (value) => (value ? true : "Space ID is required"),
	});

	const { oauthToken } = await prompts({
		type: "text",
		name: "oauthToken",
		message: "Enter your OAuth Token:",
		validate: (value) => (value ? true : "OAuth Token is required"),
	});

	const { previewToken } = await prompts({
		type: "text",
		name: "previewToken",
		message: "Enter your Preview Token:",
		validate: (value) => (value ? true : "Preview Token is required"),
	});

	// Read stories to delete
	const storiesPath = path.join(__dirname, "../data/stories-to-delete.json");
	const storyIds = JSON.parse(await readFile(storiesPath, "utf-8")) as number[];

	console.log(`\nFound ${storyIds.length} stories to delete`);

	const storyblok = new StoryblokManager(oauthToken, previewToken, spaceId);

	// Backup stories
	console.log("\nBacking up stories...");
	const stories = await storyblok.backupStories(storyIds);
	console.log(`✔ Backed up ${stories.length} stories`);

	// Confirm deletion
	const { confirmed } = await prompts({
		type: "confirm",
		name: "confirmed",
		message: "Are you sure you want to delete these stories?",
		initial: false,
	});

	if (!confirmed) {
		console.log("\nOperation cancelled");
		return;
	}

	// Delete stories
	console.log("\nDeleting stories...");
	await storyblok.deleteStories(storyIds);
	console.log("✔ All stories deleted successfully");
}

main().catch((error) => {
	console.error("\nError:", error);
	process.exit(1);
});
