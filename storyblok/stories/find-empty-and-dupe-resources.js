import { Storyblok } from "../storyblok.js";
import "dotenv/config";

// This script finds all the empty resources stories and the duplicated ones
const stories = await Storyblok.getAll("cdn/stories", {
	version: "draft",
	filter_query: {
		component: {
			in: "Resources Module",
		},
	},
});

const emptyStories = [];
for (const story of stories) {
	if (story.full_slug.startsWith("new-design")) continue;
	if (
		story?.content?.file ||
		story?.content?.custom_url?.cached_url ||
		story?.content?.video_url
	) {
		continue;
	} else if (story?.content?.enable_detail_page) {
		if (
			(story.content.detail_page &&
				story.content.detail_page.length &&
				story.content.detail_page[0].body &&
				story.content.detail_page[0].body.length) ||
			story.content.description
		) {
			continue;
		}
	}
	emptyStories.push(story);
}

console.log("————————————————————————————————————————————————");
console.log(`EMPTY RESOURCES (${emptyStories.length})`);
for (const emptyStory of emptyStories) {
	console.log(
		`https://app.storyblok.com/#/me/spaces/${process.env.STORYBLOK_SPACE_ID}/stories/0/0/${emptyStory.id}`
	);
}

const nonEmptyStories = [];
for (const story of stories) {
	if (emptyStories.includes(story)) continue;
	nonEmptyStories.push(story);
}

function findDuplicates(array) {
	const groupedByName = array.reduce((acc, obj) => {
		(acc[obj.name] = acc[obj.name] || []).push(obj);
		return acc;
	}, {});

	const duplicates = Object.values(groupedByName).reduce((acc, group) => {
		if (group.length > 1) {
			acc.push(...group.map((item) => item.id));
		}
		return acc;
	}, []);

	return duplicates;
}

const duplicates = findDuplicates(nonEmptyStories).map((id) =>
	nonEmptyStories.find((story) => story.id === id)
);
console.log("————————————————————————————————————————————————");
console.log(`DUPLICATED RESOURCES (${duplicates.length})`);
for (const duplicate of duplicates) {
	console.log(
		`https://app.storyblok.com/#/me/spaces/${process.env.STORYBLOK_SPACE_ID}/stories/0/0/${duplicate.id}`
	);
}
