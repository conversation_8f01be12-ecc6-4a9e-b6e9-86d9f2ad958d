import { getAllStories } from "./storyblok";
import { json2csv } from "json-2-csv";
import { writeFileSync } from "fs";
import prompts from "prompts";
import "dotenv/config";

(async () => {
	const { accessToken } = await prompts({
		type: "text",
		name: "accessToken",
		message: "What is your Storyblok Space Preview Token?",
	});

	const { csvFilename } = await prompts({
		type: "text",
		name: "csvFilename",
		initial: "stories.csv",
		message: "What would you like to name your CSV file?",
	});

	console.log("Retrieving all stories...");
	const stories = await getAllStories(accessToken);

	console.log(`${stories.length} stories fetched. Generating CSV file...`);
	const csv = json2csv(
		stories
			.map((s) => {
				const { content, id, published_at, ...story } = s;
				return {
					id,
					...story,
					component: content.component,
					published: published_at ? "Yes" : "No",
				};
			})
			.sort((a, b) => a.id - b.id)
	);

	const filePath = `./${csvFilename
		.replaceAll(".csv", "")
		.replaceAll("/", "")}.csv`;
	writeFileSync(filePath, csv, "utf-8");
	console.log("Done! —", filePath);
})();
