import StoryblokClient, { ISbContentMangmntAPI } from "storyblok-js-client";
import { logger } from "../utils/logger";

interface StoryblokConfig {
	spaceId: string;
	oauthToken: string;
	accessToken: string;
}

export class StoryblokImporter {
	private client: StoryblokClient;
	private config: StoryblokConfig;

	constructor(config: StoryblokConfig) {
		this.config = config;
		if (!config.spaceId || !config.oauthToken || !config.accessToken) {
			throw new Error("Missing required Storyblok configuration");
		}

		this.client = new StoryblokClient({
			accessToken: config.accessToken,
			oauthToken: config.oauthToken,
			region: "eu",
			cache: {
				clear: "auto",
				type: "memory",
			},
		});
	}

	async importStories(stories: Array<ISbContentMangmntAPI<unknown>>, parentId = "0") {
		logger.info("Starting Storyblok import...");
		let successCount = 0;
		let failureCount = 0;

		for (const [index, createStory] of stories.entries()) {
			const payload: ISbContentMangmntAPI<unknown> = {
				story: {
					...createStory.story,
					parent_id: parentId,
				},
				publish: 1,
			};

			try {
				logger.progress(
					index + 1,
					stories.length,
					`Importing: ${payload.story.name}`
				);

				const response = (await this.client.post(
					`spaces/${this.config.spaceId}/stories`,
					payload
				)) as unknown as { data: { story: { id: number } } };

				if (!response?.data?.story?.id) {
					console.log(response);
					throw new Error("Story creation failed - no story ID returned");
				}

				logger.success(
					`Successfully created story with ID: ${response.data.story.id}`
				);

				successCount++;
			} catch (error) {
				const errorLog = [
					`Failed to import story: ${payload.story.name}`,
					"Error Details:",
					JSON.stringify(error, null, 2),
				].join("\n");

				logger.error(errorLog);
				failureCount++;
			}
		}

		logger.success(
			`Import complete. Success: ${successCount}, Failed: ${failureCount}`
		);

		return { successCount, failureCount };
	}
}

export const importToStoryblok = async (inputPath: string) => {
	try {
		const config = {
			spaceId: process.env.STORYBLOK_SPACE_ID,
			oauthToken: process.env.STORYBLOK_OAUTH_TOKEN,
			accessToken: process.env.STORYBLOK_ACCESS_TOKEN,
		} as StoryblokConfig;

		const importer = new StoryblokImporter(config);
		const stories: Array<ISbContentMangmntAPI<unknown>> = JSON.parse(
			await Bun.file(inputPath).text()
		);

		const parentId = process.env.STORYBLOK_PARENT_FOLDER_ID;

		if (!parentId) {
			throw new Error("Missing required parent folder ID");
		}
		return await importer.importStories(stories, parentId);
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logger.error(errorMessage);
		throw new Error(errorMessage);
	}
};
