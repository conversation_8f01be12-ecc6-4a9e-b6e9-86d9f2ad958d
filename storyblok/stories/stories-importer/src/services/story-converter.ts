import { readFileSync, writeFileSync } from 'fs';
import { mkdir } from 'fs/promises';
import { dirname } from 'path';
import { logger } from "../utils/logger";
import { CreateFaqStoryDTO, DownloadedFaqStoryblok } from '../types';

/**
 * Converts a FAQ story from the downloaded format to the format needed for import
 */
const convertFaqStory = (story: DownloadedFaqStoryblok): CreateFaqStoryDTO => ({
    story: {
        name: story.name,
        slug: story.slug,
        content: story.content,
        is_startpage: story.is_startpage || false,
        parent_id: String(story.parent_id),
        path: story.path,
        position: story.position,
    },
    publish: 1
});

/**
 * Main conversion function that processes JSON input into Storyblok-compatible format
 * Reads from input JSON file, filters for FAQ stories, converts them, and writes to output JSON file
 * Includes progress logging for monitoring conversion status
 */
export const convertFaqStories = async (
    inputPath: string,
    processedPath: string
): Promise<CreateFaqStoryDTO[]> => {
    try {
        logger.info("Starting FAQ stories conversion...");
        
        // Read and parse input file
        const fileContent = readFileSync(inputPath, 'utf-8');
        const allStories = JSON.parse(fileContent);

        if (!allStories || !Array.isArray(allStories)) {
            throw new Error("Invalid stories data");
        }

        // Filter for FAQ modules
        const faqStories = (allStories as Array<DownloadedFaqStoryblok>).filter(
            (story) => story.content.component === "Faqs Module"
        );

        logger.info(`Found ${faqStories.length} FAQ stories`);

        // Convert stories
        const convertedStories = faqStories.map((story, index) => {
            logger.progress(index + 1, faqStories.length, `Converting: ${story.name}`);
            return convertFaqStory(story);
        });

        // Ensure output directory exists and write the file
        await mkdir(dirname(processedPath), { recursive: true });
        writeFileSync(processedPath, JSON.stringify(convertedStories, null, 2));
        
        logger.success(`Converted ${convertedStories.length} stories to ${processedPath}`);
        return convertedStories;

    } catch (error) {
        logger.error(error as Error);
        throw error;
    }
};
