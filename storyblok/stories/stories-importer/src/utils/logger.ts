const colors = {
	info: "\x1b[36m", // cyan
	success: "\x1b[32m", // green
	warn: "\x1b[33m", // yellow
	error: "\x1b[31m", // red
	reset: "\x1b[0m", // reset
} as const;

export const logger = {
	info: (message: string) =>
		console.log(`${colors.info}ℹ ${message}${colors.reset}`),
	success: (message: string) =>
		console.log(`${colors.success}✓ ${message}${colors.reset}`),
	warn: (message: string) =>
		console.warn(`${colors.warn}⚠ ${message}${colors.reset}`),
	error: (message: string | Error) => {
		const errorMessage = message instanceof Error ? message.message : message;
		const lines = errorMessage.split("\n");

		// Print first line with error symbol
		console.error(`${colors.error}✖ ${lines[0]}${colors.reset}`);

		// Print remaining lines with proper indentation
		if (lines.length > 1) {
			lines.slice(1).forEach((line) => {
				console.error(`${colors.error}  ${line}${colors.reset}`);
			});
		}
	},
	progress: (current: number, total: number, message: string) =>
		console.log(
			`${colors.info}[${current}/${total}] ${message}${colors.reset}`
		),
};
