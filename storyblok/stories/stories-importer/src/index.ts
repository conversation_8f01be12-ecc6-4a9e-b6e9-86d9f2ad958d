import { join } from "path";
import { logger } from "./utils/logger";
import { importToStoryblok } from "./services/storyblok-importer";
import { convertFaqStories } from "./services/story-converter";

// Define paths
const PATHS = {
    input: 'data/deleted-stories-from-backup.json',
    processed: 'data/processed/converted-faq-stories.json',
    output: 'data/output/import-results.json'
} as const;

const main = async () => {
    try {
        logger.info("Starting FAQ stories import process");

        // Step 1: Convert stories
        logger.info("\n=== Step 1: Converting Stories ===");
        const convertedStories = await convertFaqStories(
            PATHS.input,
            PATHS.processed
        );

        // Step 2: Import to Storyblok
        logger.info("\n=== Step 2: Importing to Storyblok ===");
        const result = await importToStoryblok(PATHS.processed);

        logger.success("\n=== Import Complete ===");
        logger.info(`Total stories imported: ${result.successCount}`);

		if (result.failureCount > 0) {
			logger.warn(`Failed imports: ${result.failureCount}`);
		}
	} catch (error) {
		logger.error("\n=== Migration Failed ===");
		logger.error(JSON.stringify(error, null, 2));
		process.exit(1);
	}
};

main();
