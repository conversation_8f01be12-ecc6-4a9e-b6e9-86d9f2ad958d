import { Storyblok } from "../storyblok.js";
import "dotenv/config";

// Unpublish stories before X date
const beforeDate = [2022, 12, 31]; // YYYY, MM, DD

const stories = await Storyblok.getAll("cdn/stories", {
	filter_query: {
		component: {
			in: "News Module,eNews Module",
		},
	},
});

const filteredStories = stories.filter((story) => {
	try {
		const date = story.content.date.value.split("-");
		const year = parseInt(date[0]);
		const month = parseInt(date[1]);
		const day = parseInt(date[2]);
		return (
			year < beforeDate[0] && day <= beforeDate[2] && month <= beforeDate[1]
		);
	} catch {
		return false;
	}
});

const unpublishStory = async (id) => {
	try {
		await Storyblok.get(
			`spaces/${process.env.STORYBLOK_SPACE_ID}/stories/${id}/unpublish`
		);
		console.log("Unpublished", id);
	} catch (e) {
		console.error("[ERROR] while unpublishing story:", e.message);
	}
};
for (const story of filteredStories) {
	console.log(
		`https://app.storyblok.com/#/me/spaces/${process.env.STORYBLOK_SPACE_ID}/stories/0/0/${story.id}`
	);
	// await unpublishStory(story.id);
}
