import { getAllStories } from "./storyblok";
import { writeFileSync } from "fs";
import prompts from "prompts";
import "dotenv/config";

(async () => {
	const { accessToken } = await prompts({
		type: "text",
		name: "accessToken",
		message: "What is your Storyblok Space Preview Token?",
	});

	const { jsonFilename } = await prompts({
		type: "text",
		name: "jsonFilename",
		initial: "stories.json",
		message: "What would you like to name your JSON file?",
	});

	console.log("Retrieving all stories...");
	const stories = await getAllStories(accessToken);

	console.log(`${stories.length} stories fetched. Generating JSON file...`);
	const processedStories = stories
		.map((s) => {
			const { content, id, published_at, ...story } = s;
			return {
				id,
				...story,
				published: published_at ? "Yes" : "No",
			};
		})
		.sort((a, b) => a.id - b.id);

	const filePath = `./${jsonFilename
		.replaceAll(".json", "")
		.replaceAll("/", "")}.json`;

	writeFileSync(filePath, JSON.stringify(processedStories, null, 2), "utf-8");
	console.log("Done! —", filePath);
})();
