import StoryblokClient, {
	ISbStoriesParams,
	ISbStoryData,
	ISbStoryParams,
} from "storyblok-js-client";

export const getAllStories = async (
	accessToken: string
): Promise<ISbStoryData[]> => {
	const Storyblok = new StoryblokClient({
		accessToken,
	});

	const params: ISbStoriesParams = {
		contain_component: "Faqs Module",
	};

	const publishedStories = await Storyblok.getAll("cdn/stories", {
		version: "published",
	});

	const draftStories = await Storyblok.getAll("cdn/stories", {
		version: "draft",
	});

	// Merge published and draft stories, preferring draft versions
	const mergedStories = new Map();
	[...publishedStories, ...draftStories].forEach((story) => {
		mergedStories.set(story.id, story);
	});

	return Array.from(mergedStories.values());
};
