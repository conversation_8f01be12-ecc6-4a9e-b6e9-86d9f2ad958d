import { Storyblok } from "../storyblok";
import { writeFileSync } from "fs";

interface StoryblokApiSpace {
	id: number;
	name: string;
	updated_at: string;
}

interface Space {
	name: string;
	plan: string;
	plan_level: number;
	created_at: string;
	id: number;
	stories_count: number;
	assets_count: number;
	request_count_today: number;
	api_requests: number;
	exceeded_requests: number;
	billing_address: {
		email?: string;
		name?: string;
		company?: string;
		order_number?: string;
		tax_number?: string;
	};
	collaborators: Array<{
		user: {
			userid: string;
			friendly_name: string;
		} | null;
	}>;
	owner: {
		userid: string;
		friendly_name: string;
	};
}

interface SpacesResponse {
	spaces: StoryblokApiSpace[];
}

interface SpaceResponse {
	space: Space;
}

const escapeCSV = (value: any): string => {
	if (value === null || value === undefined) return "";
	const stringValue = String(value);
	if (
		stringValue.includes(",") ||
		stringValue.includes('"') ||
		stringValue.includes("\n")
	) {
		return `"${stringValue.replace(/"/g, '""')}"`;
	}
	return stringValue;
};

const getSpaces = async () => {
	console.log("Fetching spaces list...");
	const response = (await Storyblok.get("spaces/")) as { data: SpacesResponse };
	if (!response?.data?.spaces) {
		throw new Error("Invalid response format from spaces API");
	}
	console.log(`Found ${response.data.spaces.length} spaces`);
	return response.data.spaces;
};

const getSpaceDetails = async (spaceId: number) => {
	const response = (await Storyblok.get(`spaces/${spaceId}`)) as {
		data: SpaceResponse;
	};
	if (!response?.data?.space) {
		throw new Error(`Invalid response format for space ${spaceId}`);
	}
	return response.data.space;
};

const main = async () => {
	const startTime = Date.now();
	let processedSpaces = 0;
	let failedSpaces = 0;
	let totalUsers = 0;

	try {
		console.log("Starting space user audit...");
		const spaces = await getSpaces();
		const csvRows = [
			["Space ID", "Space Name", "User Email"].map(escapeCSV).join(","),
		];

		for (const space of spaces) {
			try {
				console.log(`Processing space: ${space.name} (${space.id})`);
				const details = await getSpaceDetails(space.id);

				if (!details) {
					throw new Error("Space details are undefined");
				}

				// Collect all users (owner + collaborators)
				const users: string[] = [];

				// Add owner
				if (details.owner?.userid) {
					users.push(details.owner.userid);
				}

				// Add collaborators
				if (details.collaborators?.length) {
					for (const collaborator of details.collaborators) {
						if (collaborator?.user?.userid) {
							users.push(collaborator.user.userid);
						}
					}
				}

				// Create one row per user
				if (users.length === 0) {
					// If no users found, create one row with empty user email
					const row = [details.id, details.name, ""].map(escapeCSV).join(",");
					csvRows.push(row);
					totalUsers++;
				} else {
					for (const userEmail of users) {
						const row = [details.id, details.name, userEmail]
							.map(escapeCSV)
							.join(",");
						csvRows.push(row);
						totalUsers++;
					}
				}

				processedSpaces++;
				console.log(`✓ Completed space: ${space.name} (${users.length} users)`);
			} catch (error) {
				failedSpaces++;
				console.error(
					`✗ Failed to process space ${space.name} (${space.id}):`,
					error
				);
			}
		}

		const outputPath = "space-users-audit.csv";
		writeFileSync(outputPath, csvRows.join("\n"));
		const duration = ((Date.now() - startTime) / 1000).toFixed(2);
		console.log(`
User audit completed!
Duration: ${duration}s
Processed spaces: ${processedSpaces}
Failed spaces: ${failedSpaces}
Total user rows: ${totalUsers}
Results saved to: ${outputPath}
`);
	} catch (error) {
		console.error("Fatal error during space user audit:", error);
		process.exit(1);
	}
};

main();
