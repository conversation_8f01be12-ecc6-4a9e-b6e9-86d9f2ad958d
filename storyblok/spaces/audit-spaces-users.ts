import { Storyblok } from "../storyblok";
import { writeFileSync } from "fs";

interface StoryblokApiSpace {
	id: number;
	name: string;
	updated_at: string;
}

interface Space {
	name: string;
	plan: string;
	plan_level: number;
	created_at: string;
	id: number;
	stories_count: number;
	assets_count: number;
	request_count_today: number;
	api_requests: number;
	exceeded_requests: number;
	billing_address: {
		email?: string;
		name?: string;
		company?: string;
		order_number?: string;
		tax_number?: string;
	};
	collaborators: Array<{
		user: {
			userid: string;
			friendly_name: string;
		} | null;
	}>;
	owner: {
		userid: string;
		friendly_name: string;
	};
}

interface CollaboratorUser {
	id: number;
	firstname: string;
	lastname: string;
	alt_email: string | null;
	avatar: string;
	userid: string;
	friendly_name: string;
}

interface Collaborator {
	user: CollaboratorUser;
	role: string;
	user_id: number;
	permissions: string[];
	allowed_paths: number[];
	field_permissions: string[];
	id: number;
	space_role_id: string;
	space_role_ids: number[];
	space_id: number;
}

interface CollaboratorsResponse {
	collaborators: Collaborator[];
}

interface SpaceRole {
	id: number;
	role: string;
	permissions: string[];
	allowed_paths: number[];
	field_permissions: string[];
}

interface SpaceRolesResponse {
	space_roles: SpaceRole[];
}

interface SpacesResponse {
	spaces: StoryblokApiSpace[];
}

interface SpaceResponse {
	space: Space;
}

const escapeCSV = (value: any): string => {
	if (value === null || value === undefined) return "";
	const stringValue = String(value);
	if (
		stringValue.includes(",") ||
		stringValue.includes('"') ||
		stringValue.includes("\n")
	) {
		return `"${stringValue.replace(/"/g, '""')}"`;
	}
	return stringValue;
};

const getSpaces = async () => {
	console.log("Fetching spaces list...");
	const response = (await Storyblok.get("spaces/")) as { data: SpacesResponse };
	if (!response?.data?.spaces) {
		throw new Error("Invalid response format from spaces API");
	}
	console.log(`Found ${response.data.spaces.length} spaces`);
	return response.data.spaces;
};

const getSpaceDetails = async (spaceId: number) => {
	const response = (await Storyblok.get(`spaces/${spaceId}`)) as {
		data: SpaceResponse;
	};
	if (!response?.data?.space) {
		throw new Error(`Invalid response format for space ${spaceId}`);
	}
	return response.data.space;
};

const getCollaborators = async (spaceId: number) => {
	const response = (await Storyblok.get(`spaces/${spaceId}/collaborators`)) as {
		data: CollaboratorsResponse;
	};
	if (!response?.data?.collaborators) {
		throw new Error(
			`Invalid response format for collaborators in space ${spaceId}`
		);
	}
	return response.data.collaborators;
};

const getSpaceRoles = async (spaceId: number) => {
	try {
		const response = (await Storyblok.get(`spaces/${spaceId}/space_roles`)) as {
			data: SpaceRolesResponse;
		};
		if (!response?.data?.space_roles) {
			return [];
		}
		return response.data.space_roles;
	} catch (error) {
		console.warn(`Could not fetch space roles for space ${spaceId}:`, error);
		return [];
	}
};

const getUserEmail = (user: CollaboratorUser): string => {
	// Prefer alt_email if it exists and is not null
	if (user.alt_email) {
		return user.alt_email;
	}

	// Fallback to userid if it looks like an email
	if (user.userid && user.userid.includes("@")) {
		return user.userid;
	}

	// If userid doesn't look like an email, return it as is (username)
	return user.userid || "";
};

const formatPermissions = (
	collaborator: Collaborator,
	spaceRoles: SpaceRole[]
): string => {
	const permissions = new Set<string>();

	// Add direct permissions from collaborator
	if (collaborator.permissions?.length) {
		collaborator.permissions.forEach((perm) => permissions.add(perm));
	}

	// Add permissions from space roles
	if (collaborator.space_role_id) {
		const role = spaceRoles.find(
			(r) => r.id.toString() === collaborator.space_role_id
		);
		if (role?.permissions?.length) {
			role.permissions.forEach((perm) => permissions.add(perm));
		}
	}

	// Add permissions from multiple space roles
	if (collaborator.space_role_ids?.length) {
		collaborator.space_role_ids.forEach((roleId) => {
			const role = spaceRoles.find((r) => r.id === roleId);
			if (role?.permissions?.length) {
				role.permissions.forEach((perm) => permissions.add(perm));
			}
		});
	}

	return Array.from(permissions).sort().join("; ") || "No permissions";
};

const main = async () => {
	const startTime = Date.now();
	let processedSpaces = 0;
	let failedSpaces = 0;
	let totalUsers = 0;

	try {
		console.log("Starting space user audit...");
		const spaces = await getSpaces();
		const csvRows = [
			["Space ID", "Space Name", "User Email", "Permissions"]
				.map(escapeCSV)
				.join(","),
		];

		for (const space of spaces) {
			try {
				console.log(`Processing space: ${space.name} (${space.id})`);
				const details = await getSpaceDetails(space.id);

				if (!details) {
					throw new Error("Space details are undefined");
				}

				// Get collaborators and space roles
				const collaborators = await getCollaborators(space.id);
				const spaceRoles = await getSpaceRoles(space.id);

				// Create one row per collaborator
				if (collaborators.length === 0) {
					// If no collaborators found, create one row with empty data
					const row = [details.id, details.name, "", "No collaborators"]
						.map(escapeCSV)
						.join(",");
					csvRows.push(row);
					totalUsers++;
				} else {
					for (const collaborator of collaborators) {
						const userEmail = getUserEmail(collaborator.user);
						const permissions = formatPermissions(collaborator, spaceRoles);

						const row = [details.id, details.name, userEmail, permissions]
							.map(escapeCSV)
							.join(",");
						csvRows.push(row);
						totalUsers++;
					}
				}

				processedSpaces++;
				console.log(
					`✓ Completed space: ${space.name} (${collaborators.length} users)`
				);
			} catch (error) {
				failedSpaces++;
				console.error(
					`✗ Failed to process space ${space.name} (${space.id}):`,
					error
				);
			}
		}

		const outputPath = "space-users-audit.csv";
		writeFileSync(outputPath, csvRows.join("\n"));
		const duration = ((Date.now() - startTime) / 1000).toFixed(2);
		console.log(`
User audit completed!
Duration: ${duration}s
Processed spaces: ${processedSpaces}
Failed spaces: ${failedSpaces}
Total user rows: ${totalUsers}
Results saved to: ${outputPath}
`);
	} catch (error) {
		console.error("Fatal error during space user audit:", error);
		process.exit(1);
	}
};

main();
