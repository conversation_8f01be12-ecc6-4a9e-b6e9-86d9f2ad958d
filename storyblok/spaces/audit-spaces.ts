import { Storyblok } from "../storyblok";
import { writeFileSync } from "fs";

interface StoryblokApiSpace {
	id: number;
	name: string;
	updated_at: string;
}

interface Space {
	name: string;
	plan: string;
	plan_level: number;
	created_at: string;
	id: number;
	stories_count: number;
	assets_count: number;
	request_count_today: number;
	api_requests: number;
	exceeded_requests: number;
	billing_address: {
		email?: string;
		name?: string;
		company?: string;
		order_number?: string;
		tax_number?: string;
	};
	collaborators: Array<{
		user: {
			userid: string;
			friendly_name: string;
		} | null;
	}>;
	owner: {
		userid: string;
		friendly_name: string;
	};
}

interface SpacesResponse {
	spaces: StoryblokApiSpace[];
}

interface SpaceResponse {
	space: Space;
}

const PLAN_MAPPING = {
	"41DGQwCGAj": "Enterprise",
	"41DGQwCGAk": "Professional",
	"41DGQwCGAl": "Starter",
	"41DGQwCGAm": "Free",
	"41DGQwCGAn": "Trial",
} as const;

const getPlanName = (planId: string): string => {
	return PLAN_MAPPING[planId as keyof typeof PLAN_MAPPING] || planId;
};

const escapeCSV = (value: any): string => {
	if (value === null || value === undefined) return "";
	const stringValue = String(value);
	if (
		stringValue.includes(",") ||
		stringValue.includes('"') ||
		stringValue.includes("\n")
	) {
		return `"${stringValue.replace(/"/g, '""')}"`;
	}
	return stringValue;
};

const getSpaces = async () => {
	console.log("Fetching spaces list...");
	const response = (await Storyblok.get("spaces/")) as { data: SpacesResponse };
	if (!response?.data?.spaces) {
		throw new Error("Invalid response format from spaces API");
	}
	console.log(`Found ${response.data.spaces.length} spaces`);
	return response.data.spaces;
};

const getSpaceDetails = async (spaceId: number) => {
	const response = (await Storyblok.get(`spaces/${spaceId}`)) as {
		data: SpaceResponse;
	};
	if (!response?.data?.space) {
		throw new Error(`Invalid response format for space ${spaceId}`);
	}
	return response.data.space;
};

const formatDate = (dateString: string) => {
	try {
		return new Date(dateString).toISOString();
	} catch {
		return dateString;
	}
};

const formatCollaborators = (collaborators: Space["collaborators"]) => {
	if (!collaborators?.length) return "No collaborators";
	return collaborators
		.map((c) => c?.user?.userid || "")
		.filter(Boolean)
		.join("; ");
};

const formatBillingAddress = (billing: Space["billing_address"]) => {
	if (!billing) return "No billing info";

	const relevantFields = {
		email: billing.email,
		name: billing.name,
		company: billing.company,
		orderNumber: billing.order_number,
		taxNumber: billing.tax_number,
	};

	const nonEmptyFields = Object.entries(relevantFields)
		.filter(([_, value]) => value != null && value !== "")
		.map(([key, value]) => `${key}: ${value}`);

	return nonEmptyFields.length ? nonEmptyFields.join(" | ") : "No billing info";
};

const formatOwner = (owner: Space["owner"]) => {
	if (!owner?.userid) return "No owner data";
	return owner.userid;
};

const main = async () => {
	const startTime = Date.now();
	let processedSpaces = 0;
	let failedSpaces = 0;
	const uniquePlans = new Set<string>();

	try {
		console.log("Starting space audit...");
		const spaces = await getSpaces();
		const csvRows = [
			[
				"Name",
				"Plan ID",
				"Plan Level",
				"Created At",
				"ID",
				"Stories Count",
				"Assets Count",
				"Request Count Today",
				"API Requests",
				"Exceeded Requests",
				"Billing Address",
				"Collaborators",
				"Owner",
			]
				.map(escapeCSV)
				.join(","),
		];

		for (const space of spaces) {
			try {
				console.log(`Processing space: ${space.name} (${space.id})`);
				const details = await getSpaceDetails(space.id);

				if (!details) {
					throw new Error("Space details are undefined");
				}

				// Collect unique plan values
				if (details.plan) {
					uniquePlans.add(details.plan);
				}

				// Log relevant data for debugging
				console.log(`Space ${space.name}:`, {
					plan: details.plan,
					planLevel: details.plan_level,
					stories: details.stories_count,
					assets: details.assets_count,
					requests: details.request_count_today,
					hasBilling: !!details.billing_address,
					collaborators: details.collaborators?.length || 0,
					owner: details.owner?.userid,
				});

				const row = [
					details.name,
					details.plan,
					details.plan_level,
					formatDate(details.created_at),
					details.id,
					details.stories_count,
					details.assets_count,
					details.request_count_today,
					details.api_requests,
					details.exceeded_requests,
					formatBillingAddress(details.billing_address),
					formatCollaborators(details.collaborators),
					formatOwner(details.owner),
				]
					.map(escapeCSV)
					.join(",");

				csvRows.push(row);
				processedSpaces++;
				console.log(`✓ Completed space: ${space.name}`);
			} catch (error) {
				failedSpaces++;
				console.error(
					`✗ Failed to process space ${space.name} (${space.id}):`,
					error
				);
			}
		}

		const outputPath = "space-audit.csv";
		writeFileSync(outputPath, csvRows.join("\n"));
		const duration = ((Date.now() - startTime) / 1000).toFixed(2);
		console.log(`
Audit completed!
Duration: ${duration}s
Processed spaces: ${processedSpaces}
Failed spaces: ${failedSpaces}
Results saved to: ${outputPath}

Unique plan values found:
${Array.from(uniquePlans).sort().join("\n")}
`);
	} catch (error) {
		console.error("Fatal error during space audit:", error);
		process.exit(1);
	}
};

main();
