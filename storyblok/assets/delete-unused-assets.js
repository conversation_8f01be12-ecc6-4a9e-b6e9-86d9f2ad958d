import { Storyblok } from "../storyblok.js";
import "dotenv/config";

async function deleteUnusedAssets() {
	try {
		console.log("Finding and deleting unused assets...");

		// Get all stories
		const allStories = await Storyblok.getAll("cdn/stories", {
			version: "draft",
		});
		console.log("Total stories:", allStories.length);

		const allStoriesString = JSON.stringify(allStories);

		// Get all assets
		const allAssets = await Storyblok.getAll(
			`spaces/${process.env.STORYBLOK_SPACE_ID}/assets`
		);
		console.log("Total assets:", allAssets.length);

		// Find unused assets
		const unusedAssets = allAssets.filter(
			(asset) =>
				!allStoriesString.includes(
					asset.filename.replace(
						`https://s3.amazonaws.com/a.storyblok.com/f/${process.env.STORYBLOK_SPACE_ID}/x/`,
						""
					)
				)
		);
		console.log(`Found ${unusedAssets.length} unused assets.`);

		// Delete unused assets
		let deletedCount = 0;
		for (const asset of unusedAssets) {
			try {
				await Storyblok.delete(
					`spaces/${process.env.STORYBLOK_SPACE_ID}/assets/${asset.id}`
				);
				console.log(`Deleted asset: ${asset.filename}`);
				deletedCount++;
			} catch (error) {
				console.error(`Error deleting asset ${asset.id}: ${error.message}`);
			}
		}

		console.log(`Successfully deleted ${deletedCount} unused assets.`);
	} catch (error) {
		console.error("An error occurred:", error);
	}
}

deleteUnusedAssets();
