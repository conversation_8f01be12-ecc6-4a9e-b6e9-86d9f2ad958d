import json
import re
from urllib.parse import unquote

def extract_filename(url):
    # Decode URL-encoded characters
    url = unquote(url)
    # List of common file extensions
    extensions = r'\.(png|jpg|jpeg|gif|bmp|tiff|pdf|doc|docx|xls|xlsx|ppt|pptx|mp3|mp4|wav|avi|mov|jfif|webp|svg|zip|rar|7z|tar|gz|bz2|tgz|txt|csv)'
    # Find the last occurrence of /filename.extension in the URL
    match = re.search(r'/([^/]+' + extensions + ')(/|$)', url, re.IGNORECASE)
    if match:
        return match.group(1)
    return None

def main():
    unique_filenames = set()
    with open('links_to_parse.txt', 'r') as input_file:
        for line in input_file:
            url = line.strip()
            filename = extract_filename(url)
            if filename:
                unique_filenames.add(filename)
                print(f"Extracted: {filename}")
            else:
                print(f"No filename found in: {url}")

    # Convert set to list for JSON serialization
    unique_filenames_list = list(unique_filenames)

    # Output the unique filenames list as a JSON array
    with open('parsed_links.json', 'w') as json_file:
        json.dump(unique_filenames_list, json_file, indent=2)

    print(f"Total unique filenames extracted: {len(unique_filenames_list)}")

if __name__ == "__main__":
    main()