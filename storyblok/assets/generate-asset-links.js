import { Storyblok } from "../storyblok.js";
import "dotenv/config";
import fs from "fs/promises";

async function generateAssetLinks() {
	try {
		console.log("Generating asset links...");
		// Get all stories

		const allStories = await Storyblok.getAll("cdn/stories", {
			version: "draft",
		});

		console.log("All stories:", allStories.length);

		const allStoriesString = JSON.stringify(allStories);

		console.log("All stories string:", allStoriesString.length);
		// Get all assets
		const allAssets = await Storyblok.getAll(
			`spaces/${process.env.STORYBLOK_SPACE_ID}/assets`
		);

		console.log("All assets:", allAssets.length);

		// Filter unused assets
		const unusedAssets = allAssets.filter(
			(asset) =>
				!allStories.includes(
					asset.filename.replace(
						`https://s3.amazonaws.com/a.storyblok.com/f/${process.env.STORYBLOK_SPACE_ID}/x/`,
						""
					)
				)
		);

		console.log(`${unusedAssets.length} Unused Assets found.`);

		// Generate links for all assets
		const assetLinks = allAssets.map((asset) => asset.filename);

		// Write links to a file
		await fs.writeFile("asset_links.txt", assetLinks.join("\n"));

		console.log(`Asset links have been written to asset_links.txt`);
	} catch (error) {
		console.error("An error occurred:", error);
	}
}

generateAssetLinks();
