import { Storyblok } from "../storyblok.js";
import { json2csv } from "json-2-csv";
import { writeFileSync } from "fs";
import "dotenv/config";

// Debug: Log the space ID
console.log("Space ID:", process.env.STORYBLOK_SPACE_ID);

async function getAllAssets() {
	try {
		console.log("Fetching all assets...");
		const response = await Storyblok.getAll(
			`spaces/${process.env.STORYBLOK_SPACE_ID}/assets`
		);

		if (response && Array.isArray(response)) {
			console.log(`Fetched ${response.length} assets.`);
			return response;
		} else {
			console.error("Unexpected response structure:", response);
			return [];
		}
	} catch (error) {
		console.error("Error fetching assets:", error);
		return [];
	}
}

async function exportAssetUrlsToCsv() {
	const assets = await getAllAssets();
	if (assets.length === 0) {
		console.log("No assets found or error occurred during fetching.");
		return;
	}

	const assetData = assets.map((asset) => ({
		id: asset.id,
		url: asset.filename,
	}));

	try {
		const csv = json2csv(assetData);
		const fileName = `storyblok_assets_${Date.now()}.csv`;
		writeFileSync(fileName, csv, "utf-8");
		console.log(`Asset URLs exported successfully to ${fileName}`);
	} catch (error) {
		console.error("Error writing CSV file:", error);
	}
}

exportAssetUrlsToCsv();
