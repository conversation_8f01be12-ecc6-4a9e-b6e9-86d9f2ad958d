import { Storyblok } from "../storyblok.js";
import "dotenv/config";

// Find unused assets
const allStories = JSON.stringify(
	await Storyblok.getAll("cdn/stories", {
		version: "draft",
	})
);
const allAssets = await Storyblok.getAll(
	`spaces/${process.env.STORYBLOK_SPACE_ID}/assets`
);
const unusedAssets = allAssets.filter(
	(asset) =>
		!allStories.includes(
			asset.filename.replace(
				`https://s3.amazonaws.com/a.storyblok.com/f/${process.env.STORYBLOK_SPACE_ID}/x/`,
				""
			)
		)
);
console.log(`${unusedAssets.length} Unused Assets.`);
