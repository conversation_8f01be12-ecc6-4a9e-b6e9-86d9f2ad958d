const fs = require("fs");
const path = require("path");
const axios = require("axios");

async function getAllDeletedAssets() {
	const allDeletedAssets = [];
	let page = 1;
	const perPage = 100;

	try {
		while (true) {
			const response = await axios.get(
				`https://mapi.storyblok.com/v1/spaces/${process.env.STORYBLOK_SPACE_ID}/assets/`,
				{
					headers: {
						Authorization: process.env.STORYBLOK_OAUTH_TOKEN,
						"Content-Type": "application/json",
					},
					params: {
						in_folder: "-1",
						page,
						per_page: perPage,
						only_deleted: true,
					},
				}
			);

			const assets = response.data.assets;
			allDeletedAssets.push(...assets);

			if (assets.length < perPage) {
				break;
			}
			page++;
		}

		console.log(
			`Fetched ${allDeletedAssets.length} deleted assets from Storyblok.`
		);
		return allDeletedAssets;
	} catch (error) {
		console.error("Error fetching deleted assets:", error.message);
		if (error.response) {
			console.error("Response status:", error.response.status);
			console.error("Response data:", error.response.data);
		}
		throw error;
	}
}

function getAssetsToRestore(allDeletedAssets, assetNamesToRestore) {
	const assetsToRestore = allDeletedAssets.filter((asset) => {
		for (const assetName of assetNamesToRestore) {
			if (asset.filename.includes(assetName)) {
				return true;
			}
		}
	});

	console.log(
		`Found ${assetsToRestore.length} assets to restore based on assetNamesToRestore.`
	);
	return assetsToRestore;
}

async function restoreAsset(asset) {
	const { id, public_url } = asset;
	try {
		await axios.post(
			`https://mapi.storyblok.com/v1/spaces/${process.env.STORYBLOK_SPACE_ID}/assets/bulk_restore`,
			{ ids: [id] },
			{
				headers: {
					"Content-Type": "application/json",
					Authorization: process.env.STORYBLOK_OAUTH_TOKEN,
				},
			}
		);
		return { id, public_url, status: "ok" };
	} catch (error) {
		return { id, public_url, status: "failed", error: error.message };
	}
}

async function restoreMissingLinks() {
	try {
		// Read assets_to_restore.json
		const assetsToRestorePath = path.join(__dirname, "assets_to_restore.json");
		const assetNamesToRestore = JSON.parse(
			fs.readFileSync(assetsToRestorePath, "utf8")
		);

		const deletedAssets = await getAllDeletedAssets();
		const assetsToRestore = getAssetsToRestore(
			deletedAssets,
			assetNamesToRestore
		);
		console.log(`Starting restoration of ${assetsToRestore.length} assets.`);

		const results = [];
		for (const asset of assetsToRestore) {
			const result = await restoreAsset(asset);
			results.push(result);
			if (result.status !== "ok") {
				console.log(`Asset ${asset.id} restoration ${result.status}`);
			}
		}

		const successfulRestores = results
			.filter((r) => r.status === "ok")
			.map((r) => r.filename);
		const failedRestores = results
			.filter((r) => r.status === "failed")
			.map((r) => ({ error: r.error, filename: r.filename }));

		fs.writeFileSync(
			"successfully_restored.json",
			JSON.stringify(successfulRestores, null, 2)
		);
		fs.writeFileSync(
			"failed_to_restore.json",
			JSON.stringify(failedRestores, null, 2)
		);

		console.log(
			`Restoration complete. Successes: ${successfulRestores.length}, Failures: ${failedRestores.length}`
		);
		console.log(
			"Results saved to successfully_restored.json and failed_to_restore.json"
		);
	} catch (error) {
		console.error("Error during restoration process:", error.message);
	}
}

restoreMissingLinks();
