import fs from 'fs/promises';
import readline from 'readline/promises';
import StoryblokClient from 'storyblok-js-client';
import path from 'path'; // Import path module

const SPACES_FILE_NAME = 'spaces_to_backup_and_delete.txt';
const BACKUP_DIR = './backups'; // Define backup directory

/**
 * Checks if a backup file was successfully created (exists and is not empty).
 * @param {string} filePath Path to the backup file.
 * @returns {Promise<boolean>} True if backup is valid, false otherwise.
 */
async function checkBackupSuccess(filePath) {
  try {
    const stats = await fs.stat(filePath);
    if (stats.size > 0) {
      return true;
    }
    console.error(`Error: Backup file ${filePath} is empty.`);
    return false;
  } catch (err) {
    console.error(`Error: Backup file ${filePath} not found or not accessible: ${err.message}`);
    return false;
  }
}

/**
 * Main function to orchestrate the backup and delete process.
 */
async function main() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  let pat;
  try {
    pat = await rl.question('Please enter your Storyblok Personal Access Token (PAT) for the Management API: ');
    if (!pat || pat.trim() === '') {
      console.error('Error: PAT cannot be empty. Exiting.');
      process.exit(1);
    }
    pat = pat.trim();
  } catch (inputError) {
    console.error('Error: Failed to read PAT:', inputError.message);
    process.exit(1);
  } finally {
    rl.close();
  }

  const managementApiClient = new StoryblokClient({
    oauthToken: pat, // For Management API
  });

  console.log('Storyblok Management API client initialized.');

  // Create backup directory if it doesn't exist
  try {
    await fs.mkdir(BACKUP_DIR, { recursive: true });
    console.log(`Info: Backup directory '${path.resolve(BACKUP_DIR)}' is ready.`);
  } catch (error) {
    console.error(`Error creating backup directory '${BACKUP_DIR}': ${error.message}`);
    process.exit(1);
  }

  let spaceIds;
  try {
    const fileContent = await fs.readFile(SPACES_FILE_NAME, 'utf-8');
    spaceIds = fileContent.split('\n').map(id => id.trim()).filter(id => id !== '');
    if (spaceIds.length === 0) {
      console.log(`Info: No space IDs found in ${SPACES_FILE_NAME}. Please check the file. Exiting.`);
      process.exit(0);
    }
  } catch (error) {
    console.error(`Error reading ${SPACES_FILE_NAME}: ${error.message}`);
    console.error('Please ensure the file exists in the same directory as the script, is readable, and contains one space ID per line. Exiting.');
    process.exit(1);
  }

  console.log(`Info: Found ${spaceIds.length} space ID(s) to process: ${spaceIds.join(', ')}`);

  for (const spaceId of spaceIds) {
    console.log(`\n--- Processing space ID: ${spaceId} ---`);

    const componentsBackupFile = path.join(BACKUP_DIR, `${spaceId}.components.json`);
    const storiesBackupFile = path.join(BACKUP_DIR, `${spaceId}.stories.json`);
    let componentsBackedUp = false;
    let storiesBackedUp = false;
    let previewToken = null;

    // 1. Backup Components (MAPI)
    try {
      console.log(`Attempting to backup components for space ${spaceId}...`);
      const response = await managementApiClient.get(`spaces/${spaceId}/components`);
      const componentsData = response.data && response.data.components ? response.data.components : response.data;
      if (!componentsData) throw new Error('No components data found in API response.');

      await fs.writeFile(componentsBackupFile, JSON.stringify(componentsData, null, 2));
      console.log(`Components data for space ${spaceId} saved to ${componentsBackupFile}`);
      componentsBackedUp = await checkBackupSuccess(componentsBackupFile);
    } catch (error) {
      console.error(`Error backing up components for space ${spaceId}: ${error.message}`);
      if (error.response && error.response.data) {
        console.error('API Error details:', JSON.stringify(error.response.data, null, 2));
      }
    }

    // 2. Get Preview Token (MAPI)
    if (componentsBackedUp) { 
      try {
        console.log(`Fetching API keys for space ${spaceId} to find preview token...`);
        const response = await managementApiClient.get(`spaces/${spaceId}/api_keys`);
        
        if (response.data && Array.isArray(response.data.api_keys)) {
          const apiKeys = response.data.api_keys;
          const previewKeyObj = apiKeys.find(key => key.access && key.access.toLowerCase() === 'private');
          
          if (previewKeyObj && previewKeyObj.token) {
            previewToken = previewKeyObj.token;
            console.log(`Preview token obtained for space ${spaceId}.`);
          } else {
            console.error(`Error: No 'private' (preview) API key found for space ${spaceId} in the api_keys list. Cannot backup stories requiring draft access.`);
            try {
              console.log(`Attempting to fetch 'first_token' for space ${spaceId} as a fallback...`);
              const spaceDetailsResponse = await managementApiClient.get(`spaces/${spaceId}`);
              if (spaceDetailsResponse.data && spaceDetailsResponse.data.space && spaceDetailsResponse.data.space.first_token) {
                previewToken = spaceDetailsResponse.data.space.first_token;
                console.warn(`Warning: Using public 'first_token' for space ${spaceId}. This may not fetch draft stories correctly.`);
              } else {
                console.error(`Error: No 'private' token found, and could not retrieve 'first_token' for space ${spaceId}.`);
              }
            } catch (fallbackError) {
              console.error(`Error fetching 'first_token' as fallback for space ${spaceId}: ${fallbackError.message}`);
            }
          }
        } else {
          console.error(`Error: Could not retrieve a valid list of API keys for space ${spaceId}. Response was:`, JSON.stringify(response.data, null, 2));
        }
      } catch (error) {
        console.error(`Error fetching API keys for space ${spaceId}: ${error.message}`);
        if (error.response && error.response.data) {
          console.error('API Error details:', JSON.stringify(error.response.data, null, 2));
        }
      }
    }

    // 3. Backup Stories (CDN API with Preview Token)
    if (componentsBackedUp && previewToken) {
      try {
        console.log(`Attempting to backup stories for space ${spaceId} using CDN API...`);
        const cdnApiClient = new StoryblokClient({
          accessToken: previewToken, 
          cache: {
            clear: 'manual', 
            type: 'none'
          }
        });
        const storiesData = await cdnApiClient.getAll('cdn/stories', { version: 'draft', per_page: 100 });
        await fs.writeFile(storiesBackupFile, JSON.stringify(storiesData, null, 2));
        console.log(`Stories data for space ${spaceId} saved to ${storiesBackupFile}`);
        storiesBackedUp = await checkBackupSuccess(storiesBackupFile);
      } catch (error) {
        console.error(`Error backing up stories for space ${spaceId}: ${error.message}`);
        // Enhanced error logging for "Unknown" errors
        console.error('Full error object during story backup:', JSON.stringify(error, Object.getOwnPropertyNames(error), 2));
        if (error.response && error.response.data) {
          console.error('API Error details (from error.response.data):', JSON.stringify(error.response.data, null, 2));
        } else if (error.toJSON) { // some error objects have a toJSON method that can be helpful
            console.error('Error toJSON():', JSON.stringify(error.toJSON(), null, 2));
        }
        if (error.stack) {
            console.error('Error stack:', error.stack);
        }
      }
    }

    // 4. Delete Space (MAPI) if both backups were successful
    if (componentsBackedUp && storiesBackedUp) {
      console.log(`Both backups successful for space ${spaceId}. Proceeding to delete...`);
      const slug = `spaces/${spaceId}`

      try {
        await managementApiClient.delete(slug);
        console.log(`SUCCESS: Space ${spaceId} deleted successfully via MAPI.`);
      } catch (error) {
        console.error(`Error deleting space ${spaceId} via MAPI - ${slug} : ${error.message}`);
        // Enhanced error logging for space deletion
        console.error('Full error object during space deletion:', JSON.stringify(error, Object.getOwnPropertyNames(error), 2));
        if (error.response && error.response.data) {
          console.error('API Error details from delete (error.response.data):', JSON.stringify(error.response.data, null, 2));
        } else if (error.toJSON) {
            console.error('Error toJSON() from delete:', JSON.stringify(error.toJSON(), null, 2));
        }
        if (error.stack) {
            console.error('Error stack during space deletion:', error.stack);
        }
        console.warn(`Warning: Deletion failed for space ${spaceId}, but backups were created: ${componentsBackupFile}, ${storiesBackupFile}`);
      }
    } else {
      console.warn(`Backups for space ${spaceId} were not fully successful. Skipping deletion.`);
      if (!componentsBackedUp) console.log(`  - Components backup failed or file was invalid for ${spaceId}.`);
      if (!previewToken && componentsBackedUp) console.log(`  - Could not obtain preview token for stories backup for ${spaceId}.`);
      if (!storiesBackedUp && previewToken) console.log(`  - Stories backup failed or file was invalid for ${spaceId}.`);
    }
  }

  console.log('\n--- Script finished processing all specified space IDs. ---');
}

main().catch(err => {
  console.error("\nAn unexpected critical error occurred in the main script flow:", err);
  process.exit(1);
}); 