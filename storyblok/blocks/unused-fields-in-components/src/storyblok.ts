import StoryblokClient from "storyblok-js-client";
import { Story, StoryblokConfig, Component } from "./types";

export interface ComponentInstance {
	name: string;
	fields: Record<string, any>;
}

export function findAllComponentInstances(content: any): ComponentInstance[] {
	const instances: ComponentInstance[] = [];

	function traverse(obj: any) {
		if (!obj || typeof obj !== "object") return;

		if (obj.component) {
			const fields = { ...obj };
			delete fields.component;
			delete fields._uid;
			instances.push({
				name: obj.component,
				fields,
			});
		}

		if (Array.isArray(obj)) {
			obj.forEach((item) => traverse(item));
		} else {
			Object.values(obj).forEach((value) => traverse(value));
		}
	}

	traverse(content);
	return instances;
}

export function findAllComponents(content: any): string[] {
	const components: string[] = [];

	function traverse(obj: any) {
		if (!obj || typeof obj !== "object") return;

		if (obj.component) {
			components.push(obj.component);
		}

		if (Array.isArray(obj)) {
			obj.forEach((item) => traverse(item));
		} else {
			Object.values(obj).forEach((value) => traverse(value));
		}
	}

	traverse(content);
	return [...new Set(components)];
}

export const getAllStories = async (
	config: StoryblokConfig
): Promise<Story[]> => {
	const Storyblok = new StoryblokClient({
		accessToken: config.previewToken,
	});

	try {
		const stories = await Storyblok.getAll("cdn/stories", {
			version: "draft",
			resolve_relations: "1",
			resolve_links: "1",
		});

		return stories;
	} catch (error) {
		console.error("Error fetching stories:", error);
		throw error;
	}
};

export const getComponents = async (
	config: StoryblokConfig
): Promise<Component[]> => {
	const ManagementClient = new StoryblokClient({
		oauthToken: config.personalAccessToken,
	});

	try {
		const response = await ManagementClient.get(
			`spaces/${config.spaceId}/components/`
		);
		return response.data.components;
	} catch (error) {
		console.error("Error fetching components:", error);
		throw error;
	}
};
