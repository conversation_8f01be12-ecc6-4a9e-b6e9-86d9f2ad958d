import {
	findAllComponentInstances,
	getAllStories,
	getComponents,
} from "./storyblok";
import { json2csv } from "json-2-csv";
import { writeFileSync } from "fs";
import prompts from "prompts";
import {
	ComponentUsage,
	StoryblokConfig,
	Story,
	Component,
	FieldUsage,
} from "./types";
import "dotenv/config";

(async () => {
	const config: StoryblokConfig = await prompts([
		{
			type: "text",
			name: "previewToken",
			message: "What is your Storyblok Preview Token?",
			validate: (value) =>
				value.length > 0 ? true : "Preview token is required",
		},
		{
			type: "text",
			name: "personalAccessToken",
			message: "What is your Storyblok Personal Access Token?",
			validate: (value) =>
				value.length > 0 ? true : "Personal access token is required",
		},
		{
			type: "text",
			name: "spaceId",
			message: "What is your Storyblok Space ID?",
			validate: (value) => (value.length > 0 ? true : "Space ID is required"),
		},
	]);

	const { csvFilename } = await prompts({
		type: "text",
		name: "csvFilename",
		initial: "component-usage.csv",
		message: "What would you like to name your CSV file?",
	});

	console.log("Retrieving all stories and components...");

	const [stories, components] = await Promise.all([
		getAllStories(config),
		getComponents(config),
	]);

	console.log(
		`${stories.length} stories and ${components.length} components fetched. Analyzing component usage...`
	);

	// Create a map to track component usage
	const componentUsageMap = new Map<string, ComponentUsage>();

	// Initialize the map with all components and their fields
	components.forEach((component: Component) => {
		const fields = new Map<string, FieldUsage>();
		Object.entries(component.schema).forEach(([fieldName, fieldDef]) => {
			fields.set(fieldName, {
				fieldName,
				fieldType: fieldDef.type,
				usedInStories: [],
			});
		});

		componentUsageMap.set(component.name, {
			componentName: component.name,
			fields,
			usedInStories: [],
		});
	});

	// Track where each component and its fields are used
	stories.forEach((story: Story) => {
		const componentInstances = findAllComponentInstances(story.content);

		componentInstances.forEach((instance) => {
			const usage = componentUsageMap.get(instance.name);
			if (usage) {
				usage.usedInStories.push({
					storyId: story.id,
					storyName: story.name,
					storySlug: story.full_slug,
				});

				// Track field usage
				Object.entries(instance.fields).forEach(([fieldName, value]) => {
					const fieldUsage = usage.fields.get(fieldName);
					if (fieldUsage) {
						fieldUsage.usedInStories.push({
							storyId: story.id,
							storyName: story.name,
							storySlug: story.full_slug,
							value,
						});
					}
				});
			}
		});
	});

	// Prepare CSV data
	const csvData: any[] = [];

	componentUsageMap.forEach((usage, componentName) => {
		// Add component-level entry
		csvData.push({
			componentName,
			fieldName: "N/A",
			fieldType: "N/A",
			totalUsages: usage.usedInStories.length,
			isUnused: usage.usedInStories.length === 0,
			usedInStories: usage.usedInStories.map((s) => s.storySlug).join(", "),
		});

		// Add field-level entries
		usage.fields.forEach((fieldUsage, fieldName) => {
			csvData.push({
				componentName,
				fieldName,
				fieldType: fieldUsage.fieldType,
				totalUsages: fieldUsage.usedInStories.length,
				isUnused: fieldUsage.usedInStories.length === 0,
				usedInStories: fieldUsage.usedInStories
					.map((s) => s.storySlug)
					.join(", "),
			});
		});
	});

	console.log("Generating CSV file...");
	const csv = json2csv(csvData);

	const filePath = `./${csvFilename
		.replaceAll(".csv", "")
		.replaceAll("/", "")}.csv`;
	writeFileSync(filePath, csv, "utf-8");

	// Print summary
	console.log("\nComponent and Field Usage Summary:");
	componentUsageMap.forEach((usage, componentName) => {
		const isUnused = usage.usedInStories.length === 0;
		console.log(`\nComponent: ${componentName} ${isUnused ? "(UNUSED)" : ""}`);
		console.log(`Total usages: ${usage.usedInStories.length}`);

		if (!isUnused) {
			console.log("Fields:");
			usage.fields.forEach((fieldUsage, fieldName) => {
				const usageCount = fieldUsage.usedInStories.length;
				const totalPossibleUses = usage.usedInStories.length;
				const usagePercentage = (
					(usageCount / totalPossibleUses) *
					100
				).toFixed(1);
				console.log(
					`  - ${fieldName} (${fieldUsage.fieldType}): ${usageCount}/${totalPossibleUses} (${usagePercentage}%)`
				);
			});

			const unusedFields = Array.from(usage.fields.values())
				.filter((f) => f.usedInStories.length === 0)
				.map((f) => f.fieldName);

			if (unusedFields.length > 0) {
				console.log("  Unused fields:", unusedFields.join(", "));
			}
		}
	});

	const totalComponents = componentUsageMap.size;
	const unusedComponents = Array.from(componentUsageMap.values()).filter(
		(usage) => usage.usedInStories.length === 0
	).length;

	console.log("\nSummary Statistics:");
	console.log(`Total Components: ${totalComponents}`);
	console.log(
		`Unused Components: ${unusedComponents} (${(
			(unusedComponents / totalComponents) *
			100
		).toFixed(1)}%)`
	);
	console.log("\nDone! —", filePath);
})();
