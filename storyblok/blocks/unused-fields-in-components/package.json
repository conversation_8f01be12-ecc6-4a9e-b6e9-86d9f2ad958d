{"name": "storyblok-live-pages", "version": "1.0.0", "type": "module", "private": true, "scripts": {"start": "tsx src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/node": "^22.0.2", "@types/prompts": "^2.4.9", "tsx": "^4.16.5"}, "dependencies": {"dotenv": "^16.4.5", "json-2-csv": "^5.5.4", "prompts": "^2.4.2", "storyblok-js-client": "^6.8.1"}}