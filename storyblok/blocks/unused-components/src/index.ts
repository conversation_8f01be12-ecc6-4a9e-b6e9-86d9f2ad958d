import { findAllComponents, getAllStories, getComponents } from "./storyblok";
import { json2csv } from "json-2-csv";
import { writeFileSync } from "fs";
import prompts from "prompts";
import { ComponentUsage, StoryblokConfig, Story, Component } from "./types";
import "dotenv/config";

(async () => {
	const config: StoryblokConfig = await prompts([
		{
			type: "text",
			name: "previewToken",
			message: "What is your Storyblok Preview Token?",
			validate: (value) =>
				value.length > 0 ? true : "Preview token is required",
		},
		{
			type: "text",
			name: "personalAccessToken",
			message: "What is your Storyblok Personal Access Token?",
			validate: (value) =>
				value.length > 0 ? true : "Personal access token is required",
		},
		{
			type: "text",
			name: "spaceId",
			message: "What is your Storyblok Space ID?",
			validate: (value) => (value.length > 0 ? true : "Space ID is required"),
		},
	]);

	const { csvFilename } = await prompts({
		type: "text",
		name: "csvFilename",
		initial: "component-usage.csv",
		message: "What would you like to name your CSV file?",
	});

	console.log("Retrieving all stories and components...");

	const [stories, components] = await Promise.all([
		getAllStories(config),
		getComponents(config),
	]);

	console.log(
		`${stories.length} stories and ${components.length} components fetched. Analyzing component usage...`
	);

	// Create a map to track component usage
	const componentUsageMap = new Map<string, ComponentUsage>();

	// Initialize the map with all components
	components.forEach((component: Component) => {
		componentUsageMap.set(component.name, {
			componentName: component.name,
			usedInStories: [],
		});
	});

	// Track where each component is used
	stories.forEach((story: Story) => {
		const componentsInStory = findAllComponents(story.content);

		componentsInStory.forEach((componentName) => {
			const usage = componentUsageMap.get(componentName);
			if (usage) {
				usage.usedInStories.push({
					storyId: story.id,
					storyName: story.name,
					storySlug: story.full_slug,
				});
			}
		});
	});

	// Find unused components
	const unusedComponents = Array.from(componentUsageMap.values())
		.filter((usage) => usage.usedInStories.length === 0)
		.map((usage) => usage.componentName);

	// Prepare CSV data
	const csvData = Array.from(componentUsageMap.values()).map((usage) => ({
		componentName: usage.componentName,
		totalUsages: usage.usedInStories.length,
		isUnused: usage.usedInStories.length === 0,
		usedInStories: usage.usedInStories.map((s) => s.storySlug).join(", "),
	}));

	console.log("Generating CSV file...");
	const csv = json2csv(csvData);

	const filePath = `./${csvFilename
		.replaceAll(".csv", "")
		.replaceAll("/", "")}.csv`;
	writeFileSync(filePath, csv, "utf-8");

	console.log("\nUnused components:");
	unusedComponents.forEach((component) => {
		console.log(`- ${component}`);
	});
	console.log(`\nTotal unused components: ${unusedComponents.length}`);
	console.log("Done! —", filePath);
})();
