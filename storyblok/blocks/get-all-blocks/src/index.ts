import { getAllBlocks } from "./storyblok";
import { json2csv } from "json-2-csv";
import { writeFileSync } from "fs";
import prompts from "prompts";
import "dotenv/config";

(async () => {
	const { accessToken } = await prompts({
		type: "text",
		name: "accessToken",
		message: "What is your Storyblok Management API Token?",
	});

	const { spaceId } = await prompts({
		type: "text",
		name: "spaceId",
		message: "What is your Storyblok Space ID?",
	});

	const { csvFilename } = await prompts({
		type: "text",
		name: "csvFilename",
		initial: "blocks.csv",
		message: "What would you like to name your CSV file?",
	});

	console.log("Retrieving all blocks...");
	const blocks = await getAllBlocks(accessToken, spaceId);
	console.log(`${blocks.length} blocks fetched. Generating CSV file...`);

	const csv = json2csv(
		blocks.map((block) => ({
			id: block.id,
			name: block.name,
			displayName: block.display_name,
			slug: block.slug,
			isNestable: block.is_nestable,
			component_group_uuid: block.component_group_uuid,
			componentGroupName: block.component_group_name,
			created_at: block.created_at,
			updated_at: block.updated_at,
		}))
	);

	const filePath = `./${csvFilename
		.replaceAll(".csv", "")
		.replaceAll("/", "")}.csv`;
	writeFileSync(filePath, csv, "utf-8");
	console.log("Done! —", filePath);
})();
