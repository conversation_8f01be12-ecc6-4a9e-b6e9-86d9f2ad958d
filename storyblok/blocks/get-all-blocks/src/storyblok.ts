import StoryblokClient from "storyblok-js-client";

interface Block {
	id: number;
	name: string;
	display_name: string;
	slug: string;
	is_nestable: boolean;
	component_group_uuid: string;
	component_group_name: string;
	created_at: string;
	updated_at: string;
}

export const getAllBlocks = async (
	accessToken: string,
	spaceId: string
): Promise<Block[]> => {
	const Storyblok = new StoryblokClient({
		oauthToken: accessToken,
	});

	try {
		const blocks = await Storyblok.getAll(`spaces/${spaceId}/components`, {});
		return blocks;
	} catch (error) {
		console.error("Error fetching blocks:", error);
		throw error;
	}
};
