export interface Story {
	id: number;
	name: string;
	created_at: string;
	updated_at: string;
	full_slug: string;
	first_published_at: string | null;
	content: {
		_uid: string;
		component: string;
		body?: any[];
		[key: string]: any;
	};
}

export interface ComponentUsage {
	id: number;
	name: string;
	created_at: string;
	updated_at: string;
	full_slug: string;
	first_published_at: string;
	component: string;
	components_used: string[];
}

export interface StoryblokConfig {
	previewToken: string;
	personalAccessToken: string;
	spaceId: string;
}
