import { findAllComponents, getAllStories, getComponents } from "./storyblok";
import { json2csv } from "json-2-csv";
import { writeFileSync } from "fs";
import prompts from "prompts";
import { ComponentUsage, StoryblokConfig } from "./types";
import "dotenv/config";

(async () => {
	const config: StoryblokConfig = await prompts([
		{
			type: "text",
			name: "previewToken",
			message: "What is your Storyblok Preview Token?",
			validate: (value) =>
				value.length > 0 ? true : "Preview token is required",
		},
		{
			type: "text",
			name: "personalAccessToken",
			message: "What is your Storyblok Personal Access Token?",
			validate: (value) =>
				value.length > 0 ? true : "Personal access token is required",
		},
		{
			type: "text",
			name: "spaceId",
			message: "What is your Storyblok Space ID?",
			validate: (value) => (value.length > 0 ? true : "Space ID is required"),
		},
	]);

	const { csvFilename } = await prompts({
		type: "text",
		name: "csvFilename",
		initial: "component-usage.csv",
		message: "What would you like to name your CSV file?",
	});

	console.log("Retrieving all stories and components...");

	const [stories, components] = await Promise.all([
		getAllStories(config),
		getComponents(config),
	]);

	console.log(
		`${stories.length} stories and ${components.length} components fetched. Analyzing component usage...`
	);

	const componentUsage: ComponentUsage[] = stories.map((story) => ({
		id: story.id,
		name: story.name,
		created_at: story.created_at,
		updated_at: story.updated_at,
		full_slug: story.full_slug,
		first_published_at: story.first_published_at || "",
		component: story.content.component,
		components_used: findAllComponents(story.content),
	}));

	console.log("Generating CSV file...");
	const csv = json2csv(componentUsage);

	const filePath = `./${csvFilename
		.replaceAll(".csv", "")
		.replaceAll("/", "")}.csv`;
	writeFileSync(filePath, csv, "utf-8");
	console.log("Done! —", filePath);
})();
