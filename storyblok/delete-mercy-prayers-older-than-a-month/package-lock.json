{"name": "mercy-unpublish", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "mercy-unpublish", "version": "1.0.0", "license": "ISC", "dependencies": {"@types/bun": "^1.1.14", "@types/prompts": "^2.4.9", "better-sqlite3": "^11.6.0", "dotenv": "^16.4.5", "json-2-csv": "^5.5.7", "prompts": "^2.4.2", "storyblok-js-client": "^6.4.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.12"}}, "node_modules/@types/better-sqlite3": {"version": "7.6.12", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/bun": {"version": "1.1.14", "resolved": "https://registry.npmjs.org/@types/bun/-/bun-1.1.14.tgz", "integrity": "sha512-opVYiFGtO2af0dnWBdZWlioLBoxSdDO5qokaazLhq8XQtGZbY4pY3/JxY8Zdf/hEwGubbp7ErZXoN1+h2yesxA==", "license": "MIT", "dependencies": {"bun-types": "1.1.37"}}, "node_modules/@types/node": {"version": "22.10.1", "license": "MIT", "dependencies": {"undici-types": "~6.20.0"}}, "node_modules/@types/prompts": {"version": "2.4.9", "license": "MIT", "dependencies": {"@types/node": "*", "kleur": "^3.0.3"}}, "node_modules/@types/ws": {"version": "8.5.13", "resolved": "https://registry.npmjs.org/@types/ws/-/ws-8.5.13.tgz", "integrity": "sha512-osM/gWBTPKgHV8XkTunnegTRIsvF6owmf5w+JtAfOw472dptdm0dlGv4xCt6GwQRcC2XVOvvRE/0bAoQcL2QkA==", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/better-sqlite3": {"version": "11.6.0", "hasInstallScript": true, "license": "MIT", "dependencies": {"bindings": "^1.5.0", "prebuild-install": "^7.1.1"}}, "node_modules/bindings": {"version": "1.5.0", "license": "MIT", "dependencies": {"file-uri-to-path": "1.0.0"}}, "node_modules/bl": {"version": "4.1.0", "license": "MIT", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/buffer": {"version": "5.7.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/bun-types": {"version": "1.1.37", "resolved": "https://registry.npmjs.org/bun-types/-/bun-types-1.1.37.tgz", "integrity": "sha512-C65lv6eBr3LPJWFZ2gswyrGZ82ljnH8flVE03xeXxKhi2ZGtFiO4isRKTKnitbSqtRAcaqYSR6djt1whI66AbA==", "license": "MIT", "dependencies": {"@types/node": "~20.12.8", "@types/ws": "~8.5.10"}}, "node_modules/bun-types/node_modules/@types/node": {"version": "20.12.14", "resolved": "https://registry.npmjs.org/@types/node/-/node-20.12.14.tgz", "integrity": "sha512-scnD59RpYD91xngrQQLGkE+6UrHUPzeKZWhhjBSa3HSkwjbQc38+q3RoIVEwxQGRw3M+j5hpNAM+lgV3cVormg==", "license": "MIT", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/bun-types/node_modules/undici-types": {"version": "5.26.5", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==", "license": "MIT"}, "node_modules/chownr": {"version": "1.1.4", "license": "ISC"}, "node_modules/decompress-response": {"version": "6.0.0", "license": "MIT", "dependencies": {"mimic-response": "^3.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/deeks": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">= 16"}}, "node_modules/deep-extend": {"version": "0.6.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/detect-libc": {"version": "2.0.3", "license": "Apache-2.0", "engines": {"node": ">=8"}}, "node_modules/doc-path": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/dotenv": {"version": "16.4.7", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/end-of-stream": {"version": "1.4.4", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/expand-template": {"version": "2.0.3", "license": "(MIT OR WTFPL)", "engines": {"node": ">=6"}}, "node_modules/file-uri-to-path": {"version": "1.0.0", "license": "MIT"}, "node_modules/fs-constants": {"version": "1.0.0", "license": "MIT"}, "node_modules/github-from-package": {"version": "0.0.0", "license": "MIT"}, "node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/ini": {"version": "1.3.8", "license": "ISC"}, "node_modules/json-2-csv": {"version": "5.5.7", "license": "MIT", "dependencies": {"deeks": "3.1.0", "doc-path": "4.1.1"}, "engines": {"node": ">= 16"}}, "node_modules/kleur": {"version": "3.0.3", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/mimic-response": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mkdirp-classic": {"version": "0.5.3", "license": "MIT"}, "node_modules/napi-build-utils": {"version": "1.0.2", "license": "MIT"}, "node_modules/node-abi": {"version": "3.71.0", "license": "MIT", "dependencies": {"semver": "^7.3.5"}, "engines": {"node": ">=10"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/prebuild-install": {"version": "7.1.2", "license": "MIT", "dependencies": {"detect-libc": "^2.0.0", "expand-template": "^2.0.3", "github-from-package": "0.0.0", "minimist": "^1.2.3", "mkdirp-classic": "^0.5.3", "napi-build-utils": "^1.0.1", "node-abi": "^3.3.0", "pump": "^3.0.0", "rc": "^1.2.7", "simple-get": "^4.0.0", "tar-fs": "^2.0.0", "tunnel-agent": "^0.6.0"}, "bin": {"prebuild-install": "bin.js"}, "engines": {"node": ">=10"}}, "node_modules/prompts": {"version": "2.4.2", "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/pump": {"version": "3.0.2", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/rc": {"version": "1.2.8", "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/semver": {"version": "7.6.3", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/simple-concat": {"version": "1.0.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/simple-get": {"version": "4.0.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"decompress-response": "^6.0.0", "once": "^1.3.1", "simple-concat": "^1.0.0"}}, "node_modules/sisteransi": {"version": "1.0.5", "license": "MIT"}, "node_modules/storyblok-js-client": {"version": "6.10.3", "license": "MIT"}, "node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/strip-json-comments": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/tar-fs": {"version": "2.1.1", "license": "MIT", "dependencies": {"chownr": "^1.1.1", "mkdirp-classic": "^0.5.2", "pump": "^3.0.0", "tar-stream": "^2.1.4"}}, "node_modules/tar-stream": {"version": "2.2.0", "license": "MIT", "dependencies": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "engines": {"node": ">=6"}}, "node_modules/tunnel-agent": {"version": "0.6.0", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/undici-types": {"version": "6.20.0", "license": "MIT"}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}}}