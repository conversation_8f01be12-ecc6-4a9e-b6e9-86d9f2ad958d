import {
	deleteStoriesWithBatching,
	generateFilenames,
	getCdnClient,
	getMapiClient,
	getPrayersOlderThanOneMonth,
	getSpaceId,
	saveToCsv,
	saveTo<PERSON>son,
} from "./helpers";

import prompts from "prompts";

import dotenv from "dotenv";

dotenv.config();
const main = async () => {
	const { accessToken } = await prompts({
		type: "text",
		name: "accessToken",
		initial: process.env.STORYBLOK_PREVIEW_TOKEN,
		message: "What is your Storyblok Space Preview Token?",
	});

	const { mapiToken } = await prompts({
		type: "text",
		name: "mapiToken",
		initial: process.env.STORYBLOK_OAUTH_TOKEN,
		message: "What is your personal token to use MAPI?",
	});

	const { userFilename } = await prompts({
		type: "text",
		name: "userFilename",
		initial: "mercy-prayers",
		message: "What would you like to name your files?",
	});

	const { batchSize } = await prompts({
		type: "number",
		name: "batchSize",
		initial: 3,
		message: "How many stories should be processed in each batch?",
	});

	const { delayBetweenBatches } = await prompts({
		type: "number",
		name: "delayBetweenBatches",
		initial: 1000,
		message: "Delay between batches (in milliseconds)?",
	});

	const {
		backupFileFullPath,
		summaryFileFullPath,
		deletedSummaryReportFileFullPath,
	} = generateFilenames(userFilename);

	const mapiClient = getMapiClient(accessToken, mapiToken);
	const cdnClient = getCdnClient(accessToken);

	const stories = await getPrayersOlderThanOneMonth(cdnClient);
	saveToJson(stories, backupFileFullPath);
	saveToCsv(stories, summaryFileFullPath);

	const spaceId = await getSpaceId(cdnClient);

	const { confirmationText } = await prompts({
		type: "text",
		name: "confirmationText",
		initial: "no",
		message: `Type 'yes' to confirm deletion of ${stories.length} stories.`,
	});

	if (confirmationText !== "yes") {
		console.log("Deletion aborted.");
		return;
	}

	const deletedStoriesResults = await deleteStoriesWithBatching(
		mapiClient,
		spaceId,
		stories,
		{
			batchSize,
			delayBetweenBatches,
		}
	);

	saveToCsv(deletedStoriesResults, deletedSummaryReportFileFullPath);
};
main();
