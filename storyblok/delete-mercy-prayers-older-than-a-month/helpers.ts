import { json2csv } from "json-2-csv";
import { writeFileSync } from "fs";
import StoryblokClient, {
	ISbComponentType,
	ISbConfig,
	ISbResponse,
	ISbResponseData,
	ISbStoriesParams,
	ISbStoryData,
} from "storyblok-js-client";
const ONE_MONTH = 30 * 24 * 60 * 60 * 1000;

export const getCdnClient = (accessToken: string) => {
	return new StoryblokClient({
		accessToken,
		cache: {
			clear: "auto",
			type: "memory",
		},
	});
};

export const getMapiClient = (accessToken: string, oauthToken: string) => {
	return new StoryblokClient({
		accessToken,
		oauthToken,
		cache: {
			clear: "auto",
			type: "memory",
		},
	});
};

export const generateFilenames = (userFilename: string) => {
	// slugify the filename
	const regex = /[^a-zA-Z0-9-]/g;
	const parsedFilename = userFilename.replace(regex, "-");

	// date string in format YYYY-MM-DD-HH-mm-ss
	const currentDateTime = new Date().toISOString().replace(/:/g, "-");

	const fullPath = "./out/" + currentDateTime + "-" + parsedFilename;

	const backupFileFullPath = fullPath + "-backup.json";

	const summaryFileFullPath = fullPath + "-found-summary.csv";

	const deletedSummaryReportFileFullPath = fullPath + "-deleted-summary.csv";

	return {
		backupFileFullPath,
		summaryFileFullPath,
		deletedSummaryReportFileFullPath,
	};
};

export const getSpaceId = async (
	cdnClient: StoryblokClient
): Promise<number> => {
	return cdnClient
		.get("cdn/spaces/me")
		.then((response) => response.data.space.id);
};

export const getPrayersOlderThanOneMonth = async (
	cdnClient: StoryblokClient
): Promise<ISbStoryData[]> => {
	console.log("Retrieving all stories...");

	const now = new Date();
	const oneMonthAgo = new Date(now.getTime() - ONE_MONTH);

	// Format date as YYYY-MM-DD HH:mm
	const formattedDate = oneMonthAgo
		.toISOString()
		.replace("T", " ")
		.substring(0, 16);

	const params: ISbStoriesParams = {
		filter_query: {
			__or: [
				{
					component: {
						like: "Prayers Module",
					},
				},
				{
					component: {
						like: "Prayer Centre Module",
					},
				},
			],
		},
	};

	const publishedStories = await cdnClient.getAll("cdn/stories", {
		version: "published",
		published_at_lt: formattedDate,
		...params,
	});

	const draftStories = await cdnClient.getAll("cdn/stories", {
		version: "draft",
		...params,
	});

	const neverPublished = draftStories.filter(
		(story) => story.published_at === null
	);

	// Merge published and draft stories, preferring draft versions
	const mergedStories = new Map();
	[...publishedStories, ...neverPublished].forEach((story) => {
		mergedStories.set(story.id, story);
	});

	return Array.from(mergedStories.values());
};

interface BatchConfig {
	batchSize: number;
	delayBetweenBatches: number;
}

type DeleteStoryResponse = {
	story: Record<string, any>;
	error?: Error;
};

const processBatch = async (
	mapiClient: StoryblokClient,
	spaceId: number,
	batch: ISbStoryData[],
	config: BatchConfig
): Promise<DeleteStoryResponse[]> => {
	const results: DeleteStoryResponse[] = [];

	const deletePromises = batch.map(async (story) => {
		try {
			const response = await mapiClient.delete(
				`spaces/${spaceId}/stories/${story.id}`,
				{}
			);
			console.log(`Successfully deleted component ${story.id}`);
			return { story: response.story };
		} catch (error) {
			console.error(`Failed to delete component ${story.id}:`, error);
			return { story: { id: story.id }, error: error as Error };
		}
	});

	const batchResults = await Promise.all(deletePromises);
	results.push(...batchResults);

	return results;
};

export const deleteStoriesWithBatching = async (
	mapiClient: StoryblokClient,
	spaceId: number,
	components: ISbStoryData[],
	config: BatchConfig
): Promise<DeleteStoryResponse[]> => {
	console.log(
		`Starting deletion of ${components.length} stories in batches of ${config.batchSize}...`
	);

	const results: DeleteStoryResponse[] = [];

	for (let i = 0; i < components.length; i += config.batchSize) {
		const batch = components.slice(i, i + config.batchSize);
		const batchResults = await processBatch(mapiClient, spaceId, batch, config);
		results.push(...batchResults);

		if (i + config.batchSize < components.length) {
			console.log(
				`Waiting ${
					config.delayBetweenBatches
				}ms before processing next batch. Progress: ${i + config.batchSize}/${
					components.length
				}`
			);
			await new Promise((resolve) =>
				setTimeout(resolve, config.delayBetweenBatches)
			);
		}
	}

	console.log("Batch deletion process completed");
	return results;
};

export const saveToCsv = async (
	stories: Array<Record<string, any>>,
	fileName: string
) => {
	console.log(`${stories.length} stories fetched. Generating CSV file...`);
	const csv = json2csv(stories);

	writeFileSync(fileName, csv, "utf-8");
	console.log("Done! —", fileName);
};

export const saveToJson = async (stories: ISbStoryData[], fileName: string) => {
	console.log(`${stories.length} stories fetched. Generating CSV file...`);

	const jsonFile = JSON.stringify(stories, null, 2);

	writeFileSync(fileName, jsonFile, "utf-8");
	console.log("Done! —", fileName);
};
